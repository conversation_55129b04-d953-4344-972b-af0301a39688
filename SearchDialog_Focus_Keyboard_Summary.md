# SearchMemberDialog 焦点和键盘管理总结

## 功能概述

为 SearchMemberDialog 添加了自动焦点获取和键盘管理功能，提升用户体验。

## 实现的功能

### ✅ 1. 弹窗弹出时搜索框自动获取焦点

**实现方式：**
```kotlin
// 在 onViewCreated 中延迟获取焦点
Handler(Looper.getMainLooper()).postDelayed({
    autoFocusSearchView()
}, 200)

private fun autoFocusSearchView() {
    binding?.searchView?.getEditText()?.apply {
        // 请求焦点
        requestFocus()
        requestFocusFromTouch()
        
        // 显示键盘
        post {
            val inputMethodManager = context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE) 
                as android.view.inputmethod.InputMethodManager
            inputMethodManager.showSoftInput(this, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
        }
    }
}
```

**特点：**
- 延迟 200ms 确保对话框完全显示后再获取焦点
- 使用 `post` 确保在主线程中执行键盘显示
- 同时调用 `requestFocus()` 和 `requestFocusFromTouch()` 确保焦点获取成功

### ✅ 2. 弹窗关闭时确保键盘关闭

**实现方式：**
```kotlin
override fun onDestroyView() {
    // 确保键盘关闭
    hideKeyboard2()
    super.onDestroyView()
    binding = null
}

override fun dismiss() {
    // 关闭对话框时确保键盘关闭
    hideKeyboard2()
    super.dismiss()
}

override fun dismissAllowingStateLoss() {
    // 关闭对话框时确保键盘关闭
    hideKeyboard2()
    super.dismissAllowingStateLoss()
}
```

**覆盖场景：**
- 正常关闭对话框 (`dismiss()`)
- 状态丢失时关闭 (`dismissAllowingStateLoss()`)
- 视图销毁时 (`onDestroyView()`)

### ✅ 3. 会员选择时关闭键盘

**实现方式：**
```kotlin
private fun setupRecyclerView() {
    memberAdapter = MemberSearchAdapter { member ->
        // 选择会员时先关闭键盘
        hideKeyboard2()
        onMemberSelectedListener?.invoke(member)
        dismissAllowingStateLoss()
    }
}
```

### ✅ 4. 搜索完成时关闭键盘

**实现方式：**
```kotlin
getEditText()?.setOnEditorActionListener { _, actionId, _ ->
    if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH ||
        actionId == android.view.inputmethod.EditorInfo.IME_ACTION_DONE) {
        val keyword = getSearchContent()
        viewModel.searchMembers(keyword)
        removeFocus()
        // 隐藏键盘
        hideKeyboard2()
        true
    } else {
        false
    }
}
```

**触发条件：**
- 用户点击键盘上的搜索按钮
- 用户点击键盘上的完成按钮

## 技术细节

### 键盘管理

**使用的扩展函数：**
- `hideKeyboard2()` - 专门为 DialogFragment 设计的键盘隐藏方法

**扩展函数实现：**
```kotlin
fun DialogFragment?.hideKeyboard2() {
    val currentFocusedView = this?.dialog?.currentFocus
    if (currentFocusedView != null) {
        val inputManager: InputMethodManager =
            this!!.requireContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputManager.hideSoftInputFromWindow(
            currentFocusedView.windowToken,
            InputMethodManager.HIDE_NOT_ALWAYS
        )
    }
}
```

### 搜索框配置

**IME 选项配置：**
```xml
<!-- 在 commonSearchStyle 中已配置 -->
<item name="android:imeOptions">actionSearch</item>
<item name="android:inputType">text</item>
<item name="android:singleLine">true</item>
```

### 焦点获取时机

**时机选择：**
1. **对话框显示后 200ms** - 确保对话框动画完成
2. **使用 Handler.postDelayed** - 避免与对话框显示动画冲突
3. **在 post 中显示键盘** - 确保在主线程中执行

## 用户体验提升

### 🎯 自动焦点
- ✅ 用户打开对话框后无需手动点击搜索框
- ✅ 键盘自动弹出，可以立即开始输入
- ✅ 减少用户操作步骤

### 🎯 智能键盘管理
- ✅ 搜索完成后自动隐藏键盘
- ✅ 选择会员后立即隐藏键盘
- ✅ 关闭对话框时确保键盘不会残留

### 🎯 流畅交互
- ✅ 延迟获取焦点避免动画冲突
- ✅ 多种关闭场景都有键盘处理
- ✅ 搜索和选择操作更加流畅

## 修改的文件

1. **SearchMemberDialog.kt**
   - 添加了 `autoFocusSearchView()` 方法
   - 重写了 `onDestroyView()`, `dismiss()`, `dismissAllowingStateLoss()` 方法
   - 在搜索框和会员选择时添加了键盘隐藏逻辑

2. **导入的扩展函数**
   - `hideKeyboard2()` - 用于 DialogFragment 的键盘隐藏

## 测试场景

### 焦点获取测试
1. ✅ 打开对话框后搜索框自动获取焦点
2. ✅ 键盘自动弹出
3. ✅ 可以立即开始输入

### 键盘关闭测试
1. ✅ 点击搜索按钮后键盘关闭
2. ✅ 选择会员后键盘关闭
3. ✅ 点击返回键关闭对话框时键盘关闭
4. ✅ 点击外部区域关闭对话框时键盘关闭

### 边界情况测试
1. ✅ 快速打开关闭对话框不会有键盘残留
2. ✅ 屏幕旋转时键盘状态正确处理
3. ✅ 系统返回键处理正确

## 注意事项

1. **延迟时间**: 200ms 的延迟是经过测试的最佳时间，既不会太快导致冲突，也不会让用户感觉迟钝

2. **线程安全**: 所有键盘操作都在主线程中执行

3. **内存泄漏**: 在 `onDestroyView` 中正确清理了 binding 引用

4. **兼容性**: 使用的 API 兼容 Android 5.0+ (API 21+)

## 后续优化建议

1. **可配置延迟时间**: 可以将 200ms 延迟时间作为参数配置
2. **动画同步**: 可以与对话框显示动画同步，而不是使用固定延迟
3. **键盘高度适配**: 可以监听键盘高度变化，调整对话框布局
