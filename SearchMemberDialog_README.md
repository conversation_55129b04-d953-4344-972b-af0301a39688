# SearchMemberDialog - 会员搜索对话框

## 概述

SearchMemberDialog 是一个用于搜索和选择会员的对话框组件，支持模糊搜索、分页加载和实时搜索功能。

## 功能特性

- ✅ **模糊搜索**: 支持通过会员昵称或手机号进行模糊搜索
- ✅ **实时搜索**: 输入时自动搜索，带防抖功能
- ✅ **分页加载**: 支持滚动加载更多数据
- ✅ **MVVM架构**: 使用ViewModel管理状态和数据
- ✅ **依赖注入**: 使用Hilt进行依赖注入
- ✅ **响应式UI**: 支持加载状态、错误状态和空状态显示

## 文件结构

```
app/src/main/java/com/metathought/food_order/casheir/ui/dialog/
├── SearchMemberDialog.kt                    # 主对话框类
├── SearchMemberViewModel.kt                 # ViewModel
├── adapter/
│   └── MemberSearchAdapter.kt              # RecyclerView适配器
└── SearchMemberDialogExample.kt            # 使用示例

app/src/main/res/
├── layout/
│   ├── dialog_search_member.xml            # 对话框布局
│   └── item_member_search.xml              # 会员项布局
├── drawable/
│   ├── background_circle_gray.xml          # 圆形灰色背景
│   ├── background_member_normal.xml        # 普通会员标签背景
│   ├── background_member_vip.xml           # VIP会员标签背景
│   ├── ic_person.xml                       # 人员图标
│   └── ic_empty_members.xml                # 空状态图标
└── values/
    ├── strings.xml                         # 字符串资源
    └── colors.xml                          # 颜色资源
```

## 使用方法

### 基本用法

```kotlin
// 显示会员搜索对话框
SearchMemberDialog.showDialog(supportFragmentManager) { selectedMember ->
    // 处理选中的会员
    println("选中会员: ${selectedMember.nickName}")
    println("手机号: ${selectedMember.telephone}")
    println("余额: ${selectedMember.balance}")
}
```

### 高级用法

```kotlin
// 检查是否已有对话框显示
val existingDialog = SearchMemberDialog.getCurrentSearchMemberDialog(supportFragmentManager)
if (existingDialog == null) {
    SearchMemberDialog.showDialog(supportFragmentManager) { selectedMember ->
        handleMemberSelection(selectedMember)
    }
}

// 手动关闭对话框
SearchMemberDialog.dismissDialog(supportFragmentManager)
```

### 在支付场景中使用

```kotlin
SearchMemberDialog.showDialog(supportFragmentManager) { selectedMember ->
    val balance = selectedMember.balance ?: 0L
    if (balance > 0) {
        // 余额充足，可以进行支付
        proceedWithPayment(selectedMember)
    } else {
        // 提示用户充值
        showRechargeDialog(selectedMember)
    }
}
```

## API 说明

### SearchMemberDialog

#### 静态方法

- `showDialog(fragmentManager: FragmentManager, onMemberSelected: ((Record) -> Unit)? = null)`
  - 显示会员搜索对话框
  - `fragmentManager`: Fragment管理器
  - `onMemberSelected`: 会员选择回调

- `getCurrentSearchMemberDialog(fragmentManager: FragmentManager): SearchMemberDialog?`
  - 获取当前显示的对话框实例

- `dismissDialog(fragmentManager: FragmentManager)`
  - 关闭对话框

### SearchMemberViewModel

#### 主要方法

- `searchMembers(keyword: String?, isRefresh: Boolean = true)`
  - 搜索会员
  - `keyword`: 搜索关键词（手机号或昵称）
  - `isRefresh`: 是否刷新（重新搜索）

- `searchMembersWithDebounce(keyword: String?, debounceTime: Long = 500)`
  - 带防抖的实时搜索
  - `debounceTime`: 防抖时间（毫秒）

- `loadMoreMembers()`
  - 加载更多数据

- `clearSearch()`
  - 清空搜索结果

#### UI状态

```kotlin
data class UIModel(
    val showLoading: Boolean? = null,        // 是否显示加载状态
    val showError: String? = null,           // 错误信息
    val memberList: List<Record>? = null,    // 会员列表
    val isRefresh: Boolean? = null,          // 是否刷新
    val hasMoreData: Boolean? = null,        // 是否有更多数据
    val selectedMember: Record? = null       // 选中的会员
)
```

## 搜索功能

### 支持的搜索类型

1. **昵称搜索**: 输入会员昵称进行模糊匹配
2. **手机号搜索**: 输入手机号进行模糊匹配
3. **空搜索**: 不输入关键词时显示所有会员

### 搜索特性

- **实时搜索**: 输入时自动触发搜索，500ms防抖
- **模糊匹配**: 支持部分匹配，不需要完全输入
- **分页加载**: 每页20条数据，滚动到底部自动加载更多
- **状态管理**: 显示加载、错误、空状态

## 自定义配置

### 修改分页大小

在 `SearchMemberViewModel` 中修改 `pageSize` 常量：

```kotlin
private val pageSize = 20  // 修改为你需要的页面大小
```

### 修改防抖时间

在调用 `searchMembersWithDebounce` 时传入自定义时间：

```kotlin
viewModel.searchMembersWithDebounce(keyword, debounceTime = 1000) // 1秒防抖
```

### 自定义会员项布局

修改 `item_member_search.xml` 文件来自定义会员项的显示样式。

## 依赖要求

- Android SDK 21+
- Kotlin
- AndroidX
- Hilt (依赖注入)
- RecyclerView
- ViewModel & LiveData
- Coroutines

## 注意事项

1. 确保在使用前已经配置好 Hilt 依赖注入
2. 需要网络权限来调用会员列表API
3. 建议在 Fragment 或 Activity 的生命周期内使用
4. 对话框会自动处理配置变化（如屏幕旋转）

## 故障排除

### 常见问题

1. **对话框不显示**
   - 检查 FragmentManager 是否正确
   - 确保在主线程调用

2. **搜索无结果**
   - 检查网络连接
   - 确认API权限和数据

3. **内存泄漏**
   - 对话框会自动处理生命周期
   - ViewModel 会在适当时机清理资源

### 调试建议

- 查看 Logcat 中的网络请求日志
- 检查 ViewModel 的状态变化
- 确认 RecyclerView 的数据更新
