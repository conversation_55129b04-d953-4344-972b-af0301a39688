package com.metathought.food_order.casheir.data.model.base.response_model.login

import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.WholeDiscountCalculationTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import timber.log.Timber
import java.math.BigDecimal
import java.util.Locale

data class UserLoginResponse(
    var url: String?,
    val name: String,
    //原始店名
    var storeName: String,
    //柬文店名
    var storeNameKH: String? = null,
    //中文店名
    var storeNameZH: String? = null,
    //英文店名
    var storeNameEN: String? = null,
    var userAccount: String,
    var userId: String,
    var token: String?,
    val address: String,
    //true是共享餐桌服务，多人点餐，收银端需要显示桌子，有同步点餐问题
    // It is a shared table service. Multiple people are ordering. The table needs to be displayed at the cashier. There is a problem with ordering simultaneously.
    //false：非共享餐桌服务，个人点餐无餐桌服务（收银端无需显示桌子， 默认进入点餐页面）
    //Non-shared table service, no table service for personal ordering (no table needs to be displayed at the cashier, the ordering page will be entered by default)
    val isTableService: Boolean,
    //true: 需要先付款，直接完成订单 You need to pay first and complete the order directly.
    //false:后付款就是允许可加菜的逻辑 Post-payment is the logic of allowing additional dishes
    var isPaymentInAdvance: Boolean,
    //value=1 , 1%
    var vatPercentage: Int,  //增值税 登录的时候获取一次，后面更具菜单变动把这个值改了

    var serviceChargePercentage: Int,  //服务费 登录的时候获取一次，后面更具菜单变动把这个值改了

    //1 man 2: female
    val sex: Int?,
    val storeId: String,

    //是否显示桌台(餐桌非共享的时候,此字段有效) 好像都有效了
    var isDisplayTable: Boolean,

    //权限集合,逗号隔: 1-完成线下支付; 2-退款; 3-查看数据报表 17 充值权限
    var permissions: String?,

    //是否自动接单 storeInfo 返回
    var autoAcceptOrders: Boolean?,

    //收银端菜单列表是否显示图片
    var cashierShowPic: Boolean?,

    //是否需要开班
    var isNeedStartShift: Boolean?,

    //是否是开班人员
    var isShiftEmployee: Boolean?,

    //开班/备用金填写记录
    var shiftLog: CashRegisterHandoverLogVo?,

    //整单减免 是否包含vat计算
    var wholeDiscountCalculationType: Int? = WholeDiscountCalculationTypeEnum.INCLUDE_VAT.id

) {

    fun getWholeDiscountCalculationType(): WholeDiscountCalculationTypeEnum {
        return WholeDiscountCalculationTypeEnum.values().firstOrNull {
            it.id == wholeDiscountCalculationType
        } ?: WholeDiscountCalculationTypeEnum.INCLUDE_VAT
    }


    fun getStoreNameByLan(): String? {
        val locale = Locale.getDefault()
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (storeNameKH.isNullOrEmpty()) {
                storeName
            } else {
                storeNameKH
            }
        } else if (locale == Locale.CHINESE) {
            if (storeNameZH.isNullOrEmpty()) {
                storeName
            } else {
                storeNameZH
            }
        } else if (locale == Locale.ENGLISH) {
            if (storeNameEN.isNullOrEmpty()) {
                storeName
            } else {
                storeNameEN
            }
        } else {
            storeName
        }
    }

    fun getStoreNameByLan(locale: Locale): String? {
        return if (locale == MyApplication.LOCALE_KHMER) {
            if (storeNameKH.isNullOrEmpty()) {
                storeName
            } else {
                storeNameKH
            }
        } else if (locale == Locale.CHINESE) {
            if (storeNameZH.isNullOrEmpty()) {
                storeName
            } else {
                storeNameZH
            }
        } else if (locale == Locale.ENGLISH) {
            if (storeNameEN.isNullOrEmpty()) {
                storeName
            } else {
                storeNameEN
            }
        } else {
            storeName
        }
    }


    fun getPermissionList(): List<String> {
        val list = permissions?.split(",") ?: listOf()
        return list
    }

    fun getCurrentVatPercentage(): Int {
        return vatPercentage ?: 0
    }

    fun getCurrentServiceChargePercentage(): Int {
        return serviceChargePercentage ?: 0
    }


}

data class PermissionResponse(
    var id: Long?,
    var permissions: String?,
    //进销存权限集合,逗号隔开 1-入库管理;2-出库管理;3-盘点-发起盘点;4-盘点调整库存 5-基础信息配置 6査看库存报表
    var psiPermissions: String?,
)
