package com.metathought.food_order.casheir.ui.table.reserve

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.DialogAvailableBinding
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment


class AvailableTableDialog :  BaseDialogFragment() {
    private var binding: DialogAvailableBinding? = null
    private var reserveButtonListener: ((String) -> Unit)? = null
    private var orderButtonListener: (() -> Unit)? = null
    private var printerButtonListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogAvailableBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }


    private fun initData() {
        val content = Gson().fromJson(arguments?.getString(CONTENT), TableResponseItem::class.java)

        binding?.apply {
            tvTableID.text =content.name
            if(content.type == 2) {//通用桌不显示 Universal table not showing
                btnReserve.isVisible = false
                cardPrinter.isVisible = false
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            btnReserve.setOnClickListener {
                val id = arguments?.getString(ID)
                if (id != null) {
                    reserveButtonListener?.invoke(id)
                }
                dismissAllowingStateLoss()
            }
            btnOrder.setOnClickListener {
                orderButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
            cardPrinter.setOnClickListener {
                //打印临时桌码 Print temporary table code
                printerButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        private const val RESERVE_DIALOG = "RESERVE_DIALOG"
        private const val CONTENT = "CONTENT"
        private const val ID = "ID"

        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            id: String? = null,
            orderButtonListener: (() -> Unit),
            reserveButtonListener: ((String) -> Unit),
            printerButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(RESERVE_DIALOG)
            if (fragment != null) return
            fragment = newInstance(
                iOrderListener = orderButtonListener,
                iCancelListener = reserveButtonListener,
                iPrinterButtonListener=printerButtonListener,
                content = content,
                id = id
            )
            fragment.show(fragmentManager, RESERVE_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(RESERVE_DIALOG) as? AvailableTableDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iCancelListener: ((String) -> Unit),
            iOrderListener: (() -> Unit),
            iPrinterButtonListener: (() -> Unit),
            content: String? = null,
            id: String? = null
        ): AvailableTableDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            args.putString(ID, id)
            val fragment = AvailableTableDialog()
            fragment.reserveButtonListener = iCancelListener
            fragment.orderButtonListener = iOrderListener
            fragment.printerButtonListener = iPrinterButtonListener
            fragment.arguments = args
            return fragment
        }
    }

}
