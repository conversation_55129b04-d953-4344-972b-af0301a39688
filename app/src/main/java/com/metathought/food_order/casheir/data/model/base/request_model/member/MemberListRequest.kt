package com.metathought.food_order.casheir.data.model.base.request_model.member


import com.google.gson.annotations.SerializedName
import retrofit2.http.Query

data class MemberListRequest(
    @SerializedName("pageSize")
    val pageSize: Int?,
    @SerializedName("page")
    val page: Int?,
    @SerializedName("upayAccount")
    val upayAccount: String?,
    @SerializedName("userType")
    val userType: String?,
    @SerializedName("startTime")
    val startTime: String?,
    @SerializedName("endTime")
    val endTime: String?,
)