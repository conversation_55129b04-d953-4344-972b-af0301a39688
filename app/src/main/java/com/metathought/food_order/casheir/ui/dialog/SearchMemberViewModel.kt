package com.metathought.food_order.casheir.ui.dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.request_model.ConsumerPayAccountV2Request
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class SearchMemberViewModel @Inject constructor(
    private val repository: Repository
) : ViewModel() {

    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    private var searchJob: Job? = null
    private var currentPage = 1
    private val pageSize = 20
    private var currentKeyword: String? = null
    private var hasMoreData = true

    /**
     * 搜索会员 - 支持模糊搜索
     * @param keyword 搜索关键词（手机号或昵称）
     * @param isRefresh 是否刷新（重新搜索）
     */
    fun searchMembers(keyword: String?, isRefresh: Boolean = true) {
        // 取消之前的搜索任务
        searchJob?.cancel()

        searchJob = viewModelScope.launch {
            // 如果是新的搜索或刷新，重置分页
            if (isRefresh || keyword != currentKeyword) {
                currentPage = 1
                currentKeyword = keyword
                hasMoreData = true
                emitUiState(showLoading = true)
            }

            // 如果没有更多数据，直接返回
            if (!hasMoreData && !isRefresh) {
                return@launch
            }

            try {
                val request = ConsumerPayAccountV2Request(
                    upayAccount = keyword?.trim()?.takeIf { it.isNotEmpty() },
                    exactMatch = false, // 模糊搜索
                    page = currentPage,
                    pageSize = pageSize
                )
                val response = repository.consumerPayAccountV2(request)

                withContext(Dispatchers.Main) {
                    when (response) {
                        is ApiResponse.Success -> {
                            val memberList = response.data.records
                            val newRecords = memberList ?: emptyList()

                            // 检查是否还有更多数据
                            hasMoreData = newRecords.size >= pageSize

                            if (isRefresh || keyword != currentKeyword) {
                                // 新搜索，替换数据
                                emitUiState(
                                    showLoading = false,
                                    memberList = newRecords,
                                    isRefresh = true,
                                    hasMoreData = hasMoreData
                                )
                            } else {
                                // 加载更多，追加数据
                                emitUiState(
                                    showLoading = false,
                                    memberList = newRecords,
                                    isRefresh = false,
                                    hasMoreData = hasMoreData
                                )
                            }

                            currentPage++
                        }

                        is ApiResponse.Error -> {
                            emitUiState(
                                showLoading = false,
                                showError = response.message ?: "搜索失败"
                            )
                        }

                        is ApiResponse.Loading -> {
                            emitUiState(showLoading = true)
                        }
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    emitUiState(
                        showLoading = false,
                        showError = e.message ?: "网络错误"
                    )
                }
            }
        }
    }

    /**
     * 实时搜索 - 带防抖功能
     * @param keyword 搜索关键词
     * @param debounceTime 防抖时间（毫秒）
     */
    fun searchMembersWithDebounce(keyword: String?, debounceTime: Long = 500) {
        searchJob?.cancel()

        // 如果关键词为空，直接返回，不执行搜索
        if (keyword.isNullOrEmpty()) {
            return
        }

        searchJob = viewModelScope.launch {
            delay(debounceTime)
            searchMembers(keyword, isRefresh = true)
        }
    }

    /**
     * 加载更多数据
     */
    fun loadMoreMembers() {
        if (hasMoreData && searchJob?.isActive != true) {
            searchMembers(currentKeyword, isRefresh = false)
        }
    }

    /**
     * 清空搜索结果
     */
    fun clearSearch() {
        searchJob?.cancel()
        currentKeyword = null
        currentPage = 1
        hasMoreData = true
        emitUiState(
            showLoading = false,
            memberList = emptyList(),
            isRefresh = true
        )
    }

    private fun emitUiState(
        showLoading: Boolean? = null,
        showError: String? = null,
        memberList: List<CustomerMemberResponse>? = null,
        isRefresh: Boolean? = null,
        hasMoreData: Boolean? = null,
        selectedMember: CustomerMemberResponse? = null
    ) {
        val uiModel = UIModel(
            showLoading = showLoading,
            showError = showError,
            memberList = memberList,
            isRefresh = isRefresh,
            hasMoreData = hasMoreData,
            selectedMember = selectedMember
        )
        _uiState.value = uiModel
    }

    /**
     * UI 状态数据类
     */
    data class UIModel(
        val showLoading: Boolean? = null,
        val showError: String? = null,
        val memberList: List<CustomerMemberResponse>? = null,
        val isRefresh: Boolean? = null,
        val hasMoreData: Boolean? = null,
        val selectedMember: CustomerMemberResponse? = null
    )

    override fun onCleared() {
        super.onCleared()
        searchJob?.cancel()
    }
}
