package com.metathought.food_order.casheir.ui.pending_order.resume_dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView.OnItemClickListener
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.databinding.DialogResumeOrderBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.ResumePendingOrderAdapter
import com.metathought.food_order.casheir.ui.order.food_detail.SubDetailDialog
import com.metathought.food_order.casheir.ui.pending_order.PendingOrderFragmentDialog
import com.metathought.food_order.casheir.ui.pending_order.PendingOrderViewModel
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/3/1920:20
 * @description
 */
@AndroidEntryPoint
class ResumeOrderDialog : DialogFragment() {

    private var binding: DialogResumeOrderBinding? = null
    private val viewModel: PendingOrderViewModel by viewModels()
    private lateinit var pendingOrderAdapter: ResumePendingOrderAdapter
    private var diningStyleEnum = 0
    private lateinit var onItemClickListener: () -> Unit

    private var isRequest = false

    companion object {
        private const val SUB_FOOD_LIST = "SUB_FOOD_LIST"
        private const val DINING_STYLE = "DINING_STYLE"
        fun showDialog(
            fragmentManager: FragmentManager,
            diningStyleEnum: Int,
            onItemClickListener: () -> Unit
        ) {
            var fragment = fragmentManager.findFragmentByTag(SUB_FOOD_LIST)
            if (fragment != null) return
            fragment = newInstance(diningStyleEnum, onItemClickListener)
            fragment.show(fragmentManager, SUB_FOOD_LIST)

        }

        private fun newInstance(
            diningStyleEnum: Int,
            onItemClickListener: () -> Unit
        ): ResumeOrderDialog {
            val args = Bundle().apply {
                putInt(DINING_STYLE, diningStyleEnum)
            }
            val fragment = ResumeOrderDialog()
            fragment.arguments = args
            fragment.onItemClickListener = onItemClickListener
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogResumeOrderBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
//            binding?.apply {
//                dialog?.window?.setLayout(root.measuredWidth, root.measuredHeight)
//            }
            val screenHeight = (displayMetrics.heightPixels * 0.5).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.3).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {
        viewModel.uiListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
//                    if (it)
//                        showProgress()
                }
                if (it.showEnd) {
//                    dismissProgress()
                    if (it.isRefresh != false) {
                        layoutEmptyList.root.isVisible = true
                        pendingOrderAdapter?.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
//                    dismissProgress()
                    if (error.isNotEmpty())
                        Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
                }

                it.showSuccess?.let { response ->
//                    dismissProgress()
                    layoutEmptyList.root.isVisible = false
                    if (it.isRefresh != false) {
                        ArrayList(response.records).let { it1 ->
                            pendingOrderAdapter.replaceData(
                                it1
                            )
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(response.records).let { it1 -> pendingOrderAdapter.addData(it1) }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }



        viewModel.uiGetRecordState.observe(viewLifecycleOwner) {
            it?.let {
                when (it.pendingRecord) {
                    is ApiResponse.Loading -> {

                    }

                    is ApiResponse.Success -> {
                        // dismissProgress()
                        if (it.pendingRecord.data == null) {
                            it.record?.getPendingGoodJson()?.goodsList?.clear()
                        } else {
                            it.record = it.pendingRecord.data
                        }


                        val reserveTableRequest = ReserveTableRequest(
                            diningTime = it.record?.getCustomerJson()?.diningTime ?: "",
                            diningNumber = it.record?.getCustomerJson()?.diningNumber ?: 0,
                            areaCode = it.record?.getCustomerJson()?.areaCode ?: "",
                            mobile = it.record?.getCustomerJson()?.mobile ?: "",
                            name = it.record?.getCustomerJson()?.name ?: "",
                            tableId = ""
                        )

                        it.record?.getPendingGoodJson()?.goodsList?.let { it1 ->
                            viewModel.updateShoppingRecord(
                                reserveTableRequest, diningStyleEnum,
                                it1, it.record?.orderNote
                            )
                        }
                        dismissAllowingStateLoss()
                        onItemClickListener.invoke()

                    }


                    is ApiResponse.Error -> {
//                        dismissProgress()
                        if (it.pendingRecord.message?.isNotEmpty() == true)
                            Toast.makeText(context, it.pendingRecord.message, Toast.LENGTH_SHORT)
                                .show()

                        isRequest = false
                    }

                    else -> {}
                }


            }


        }


    }

    private fun initListener() {
        binding?.apply {
            context?.let {
            }
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    viewModel.getPendingList(true, diningStyleList = arrayListOf(diningStyleEnum))
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    viewModel.getPendingList(false, diningStyleList = arrayListOf(diningStyleEnum))
                }

            })
        }

    }

    private fun initData() {
        diningStyleEnum = arguments?.getInt(DINING_STYLE) ?: 0
        viewModel.getPendingList(diningStyleList = arrayListOf(diningStyleEnum))
        binding?.apply {
            pendingOrderAdapter = ResumePendingOrderAdapter(arrayListOf(),
                onItemClickListener = { record ->
                    SingleClickUtils.isFastDoubleClick {
                        if (!isRequest) {
                            isRequest = true
                            record.id?.let { it1 -> viewModel.getPendingOrderByNo(it1, record) }
                        }
                    }

                })

            recyclerPendingOrder.adapter = pendingOrderAdapter
        }

    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

}