package com.metathought.food_order.casheir.ui.dialog.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.ItemMemberSearchBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils

class MemberSearchAdapter(
    private val onMemberClick: (CustomerMemberResponse) -> Unit
) : ListAdapter<CustomerMemberResponse, MemberSearchAdapter.MemberViewHolder>(MemberDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding = ItemMemberSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class MemberViewHolder(
        private val binding: ItemMemberSearchBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(member: CustomerMemberResponse) {
            binding.apply {
                // 设置会员昵称
                tvMemberName.text = member.nickName ?: ""

                // 设置手机号
                tvMemberPhone.text = member.telephone ?: ""

                // 设置余额
                val balance = member.balance ?: 0L
                tvMemberBalance.text = balance.priceFormatTwoDigitZero2()

                // 设置点击事件
                root.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        onMemberClick(member)
                    }
                }
            }
        }
    }

    /**
     * DiffUtil 回调，用于高效更新列表
     */
    private class MemberDiffCallback : DiffUtil.ItemCallback<CustomerMemberResponse>() {
        override fun areItemsTheSame(
            oldItem: CustomerMemberResponse,
            newItem: CustomerMemberResponse
        ): Boolean {
            return oldItem.accountId == newItem.accountId
        }

        override fun areContentsTheSame(
            oldItem: CustomerMemberResponse,
            newItem: CustomerMemberResponse
        ): Boolean {
            return oldItem == newItem
        }
    }

    /**
     * 添加数据到现有列表（用于分页加载）
     */
    fun addData(newData: List<CustomerMemberResponse>) {
        val currentList = currentList.toMutableList()
        currentList.addAll(newData)
        submitList(currentList) {
            // 提交完成后的回调，确保数据已更新
        }
    }

    /**
     * 替换所有数据（用于新搜索）
     */
    fun replaceData(newData: List<CustomerMemberResponse>) {
        // 先提交 null 来强制清空，然后提交新数据
        submitList(null) {
            submitList(newData.toList())
        }
    }

    /**
     * 清空数据
     */
    fun clearData() {
        submitList(null) {
            submitList(emptyList())
        }
    }

    /**
     * 获取指定位置的会员数据
     */
    fun getMemberAt(position: Int): CustomerMemberResponse? {
        return if (position in 0 until itemCount) {
            getItem(position)
        } else {
            null
        }
    }

    /**
     * 根据ID查找会员
     */
    fun findMemberById(memberId: String): CustomerMemberResponse? {
        return currentList.find { it.accountId == memberId }
    }

    /**
     * 高亮搜索关键词（如果需要）
     */
    private fun highlightSearchKeyword(text: String, keyword: String?): String {
        if (keyword.isNullOrEmpty() || !text.contains(keyword, ignoreCase = true)) {
            return text
        }
        // 这里可以添加高亮逻辑，比如使用 SpannableString
        return text
    }
}
