package com.metathought.food_order.casheir.ui.dialog.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.ItemMemberSearchBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils

class MemberSearchAdapter(
    private val onMemberClick: (CustomerMemberResponse) -> Unit
) : ListAdapter<CustomerMemberResponse, MemberSearchAdapter.MemberViewHolder>(MemberDiffCallback()) {

    // 添加一个内部列表来跟踪数据
    private var internalList = mutableListOf<CustomerMemberResponse>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding = ItemMemberSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class MemberViewHolder(
        private val binding: ItemMemberSearchBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(member: CustomerMemberResponse) {
            binding.apply {
                // 设置会员昵称
                tvMemberName.text = member.nickName ?: ""

                // 设置手机号
                tvMemberPhone.text = member.telephone ?: ""

                // 设置余额
                val balance = member.balance ?: 0L
                tvMemberBalance.text = balance.priceFormatTwoDigitZero2()

                // 设置点击事件
                root.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        onMemberClick(member)
                    }
                }
            }
        }
    }

//    /**
//     * DiffUtil 回调，用于高效更新列表
//     */
//    private class MemberDiffCallback : DiffUtil.ItemCallback<CustomerMemberResponse>() {
//        override fun areItemsTheSame(
//            oldItem: CustomerMemberResponse,
//            newItem: CustomerMemberResponse
//        ): Boolean {
//            return oldItem.accountId == newItem.accountId
//        }
//
//        override fun areContentsTheSame(
//            oldItem: CustomerMemberResponse,
//            newItem: CustomerMemberResponse
//        ): Boolean {
//            return oldItem == newItem
//        }
//    }

    /**
     * 添加数据到现有列表（用于分页加载）
     */
    fun addData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "addData: 当前${internalList.size}条, 新增${newData.size}条")

        internalList.addAll(newData)
        val newList = ArrayList(internalList)

        submitList(newList) {
            android.util.Log.d("MemberAdapter", "addData完成: 适配器数量=${itemCount}")
        }
    }

    /**
     * 替换所有数据（用于新搜索）
     */
    fun replaceData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "replaceData: 新数据${newData.size}条")

        internalList.clear()
        internalList.addAll(newData)

        // 直接提交新数据，避免多次闪烁
        submitList(ArrayList(newData))
    }

    /**
     * 清空数据
     */
    fun clearData() {
        android.util.Log.d("MemberAdapter", "clearData: 清空数据")

        internalList.clear()

        // 直接提交空列表，避免多次闪烁
        submitList(emptyList())
    }

    /**
     * 获取指定位置的会员数据
     */
    fun getMemberAt(position: Int): CustomerMemberResponse? {
        return if (position in 0 until itemCount) {
            getItem(position)
        } else {
            null
        }
    }

    /**
     * 根据ID查找会员
     */
    fun findMemberById(memberId: String): CustomerMemberResponse? {
        return currentList.find { it.accountId == memberId }
    }

    /**
     * 高亮搜索关键词（如果需要）
     */
    private fun highlightSearchKeyword(text: String, keyword: String?): String {
        if (keyword.isNullOrEmpty() || !text.contains(keyword, ignoreCase = true)) {
            return text
        }
        // 这里可以添加高亮逻辑，比如使用 SpannableString
        return text
    }
}
