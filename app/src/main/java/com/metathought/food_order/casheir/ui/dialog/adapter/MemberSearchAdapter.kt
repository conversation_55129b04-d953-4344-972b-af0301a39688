package com.metathought.food_order.casheir.ui.dialog.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.ItemMemberSearchBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils

class MemberSearchAdapter(
    private val onMemberClick: (CustomerMemberResponse) -> Unit
) : ListAdapter<CustomerMemberResponse, MemberSearchAdapter.MemberViewHolder>(MemberDiffCallback()) {

    // 添加一个内部列表来跟踪数据
    private var internalList = mutableListOf<CustomerMemberResponse>()

    // 添加一个标志来跟踪是否正在更新
    private var isUpdating = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding = ItemMemberSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class MemberViewHolder(
        private val binding: ItemMemberSearchBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(member: CustomerMemberResponse) {
            binding.apply {
                // 设置会员昵称
                tvMemberName.text = member.nickName ?: ""

                // 设置手机号
                tvMemberPhone.text = member.telephone ?: ""

                // 设置余额
                val balance = member.balance ?: 0L
                tvMemberBalance.text = balance.priceFormatTwoDigitZero2()

                // 设置点击事件
                root.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        onMemberClick(member)
                    }
                }
            }
        }
    }

    /**
     * DiffUtil 回调，用于高效更新列表
     */
    private class MemberDiffCallback : DiffUtil.ItemCallback<CustomerMemberResponse>() {
        override fun areItemsTheSame(
            oldItem: CustomerMemberResponse,
            newItem: CustomerMemberResponse
        ): Boolean {
            return oldItem.accountId == newItem.accountId
        }

        override fun areContentsTheSame(
            oldItem: CustomerMemberResponse,
            newItem: CustomerMemberResponse
        ): Boolean {
            // 比较关键字段，避免不必要的更新
            return oldItem.accountId == newItem.accountId &&
                    oldItem.nickName == newItem.nickName &&
                    oldItem.telephone == newItem.telephone &&
                    oldItem.balance == newItem.balance &&
                    oldItem.paymentMethod == newItem.paymentMethod
        }
    }

    /**
     * 添加数据到现有列表（用于分页加载）
     */
    fun addData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "addData: 当前${internalList.size}条, 新增${newData.size}条")

        internalList.addAll(newData)
        val newList = ArrayList(internalList)

        submitList(newList) {
            android.util.Log.d("MemberAdapter", "addData完成: 适配器数量=${itemCount}")
        }
    }

    /**
     * 替换所有数据（用于新搜索）
     */
    fun replaceData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d("MemberAdapter", "replaceData: 新数据${newData.size}条, 当前列表${currentList.size}条")

        // 防止并发更新
        if (isUpdating) {
            android.util.Log.w("MemberAdapter", "正在更新中，跳过本次更新")
            return
        }

        isUpdating = true
        internalList.clear()
        internalList.addAll(newData)

        // 创建新的列表实例，确保引用不同
        val newList = ArrayList(newData)

        // 强制刷新策略：总是先提交null，再提交新数据
        submitList(null) {
            submitList(newList) {
                isUpdating = false
                android.util.Log.d("MemberAdapter", "replaceData完成: 目标${newData.size}条, 实际${itemCount}条")

                // 如果数量不匹配，强制使用 notifyDataSetChanged
                if (itemCount != newData.size) {
                    android.util.Log.w("MemberAdapter", "数量不匹配，使用notifyDataSetChanged强制刷新")
                    notifyDataSetChanged()
                }
            }
        }
    }

    /**
     * 清空数据
     */
    fun clearData() {
        android.util.Log.d("MemberAdapter", "clearData: 清空数据")

        internalList.clear()

        // 直接提交空列表，避免多次闪烁
        submitList(emptyList())
    }

    /**
     * 获取指定位置的会员数据
     */
    fun getMemberAt(position: Int): CustomerMemberResponse? {
        return if (position in 0 until itemCount) {
            getItem(position)
        } else {
            null
        }
    }

    /**
     * 根据ID查找会员
     */
    fun findMemberById(memberId: String): CustomerMemberResponse? {
        return currentList.find { it.accountId == memberId }
    }

    /**
     * 高亮搜索关键词（如果需要）
     */
    private fun highlightSearchKeyword(text: String, keyword: String?): String {
        if (keyword.isNullOrEmpty() || !text.contains(keyword, ignoreCase = true)) {
            return text
        }
        // 这里可以添加高亮逻辑，比如使用 SpannableString
        return text
    }
}
