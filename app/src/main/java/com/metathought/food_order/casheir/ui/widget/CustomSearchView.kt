package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.LayoutInflater

import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.ViewCustomSearchBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.utils.SingleClickUtils


class CustomSearchView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private var _binding: ViewCustomSearchBinding? = null

    init {

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomSearchView)

        try {
            val hint = typedArray.getString(
                R.styleable.CustomSearchView_search_hint
            )

            initView()

            _binding?.apply {
                edtSearch.hint = hint
            }
        } catch (e: Exception) {

        } finally {
            // 最后需要回收数组
            typedArray.recycle()
        }
    }

    private fun initView() {
        _binding = ViewCustomSearchBinding.inflate(LayoutInflater.from(context), this, true)
        _binding?.apply {

            edtSearch.setOnFocusChangeListener { view, isFocus ->
                if (isFocus) {
                    edtSearch.setBackgroundResource(R.drawable.background_border_primary_25dp)
                } else {
                    edtSearch.setBackgroundResource(R.drawable.background_language_spiner)
                }
            }
            edtSearch.setOnEditorActionListener { textView, i, keyEvent ->
                if (i == EditorInfo.IME_ACTION_SEARCH) {
                    context.hideKeyboard(edtSearch)
                    true
                }
                false
            }

            ivClear.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (edtSearch.text.toString().isNotEmpty()) {
                        edtSearch.setText("")
                    }
                }
            }
        }
    }

    fun getSearchContent(): String {
        return _binding?.edtSearch?.text.toString()
    }

    fun setSearchContent(content: String) {
        _binding?.edtSearch?.setText(content)
    }

    fun getEditText(): EditText? {
        return _binding?.edtSearch
    }

    fun setTextChangedListenerCallBack(callback: (() -> Unit)? = null) {
        _binding?.apply {
            edtSearch.addTextChangedListener {
                ivClear.isVisible = edtSearch.text.isNotEmpty()
                callback?.invoke()
            }
        }
    }

    fun removeFocus() {
        _binding?.edtSearch?.apply {
            this.clearFocus()
            context.hideKeyboard(this)
        }

    }
}