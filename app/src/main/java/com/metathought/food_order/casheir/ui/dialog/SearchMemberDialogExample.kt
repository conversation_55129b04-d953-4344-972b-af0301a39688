package com.metathought.food_order.casheir.ui.dialog

import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record

/**
 * SearchMemberDialog 使用示例
 * 
 * 这个类展示了如何使用 SearchMemberDialog 进行会员搜索和选择
 */
object SearchMemberDialogExample {

    /**
     * 显示会员搜索对话框的基本用法
     * 
     * @param fragmentManager Fragment管理器
     */
    fun showBasicExample(fragmentManager: FragmentManager) {
        SearchMemberDialog.showDialog(fragmentManager) { selectedMember ->
            // 处理选中的会员
            handleSelectedMember(selectedMember)
        }
    }

    /**
     * 显示会员搜索对话框的高级用法
     * 
     * @param fragmentManager Fragment管理器
     */
    fun showAdvancedExample(fragmentManager: FragmentManager) {
        // 获取当前显示的对话框实例（如果存在）
        val existingDialog = SearchMemberDialog.getCurrentSearchMemberDialog(fragmentManager)
        if (existingDialog != null) {
            // 如果对话框已经显示，可以选择关闭或者不做任何操作
            return
        }

        // 显示新的对话框
        SearchMemberDialog.showDialog(fragmentManager) { selectedMember ->
            // 处理选中的会员
            handleSelectedMember(selectedMember)
            
            // 可以在这里添加更多的业务逻辑
            // 比如：更新UI、保存选择、跳转页面等
        }
    }

    /**
     * 手动关闭会员搜索对话框
     * 
     * @param fragmentManager Fragment管理器
     */
    fun dismissDialog(fragmentManager: FragmentManager) {
        SearchMemberDialog.dismissDialog(fragmentManager)
    }

    /**
     * 处理选中的会员
     * 
     * @param member 选中的会员信息
     */
    private fun handleSelectedMember(member: Record) {
        // 这里可以根据业务需求处理选中的会员
        println("选中的会员信息:")
        println("ID: ${member.id}")
        println("昵称: ${member.nickName}")
        println("手机号: ${member.telephone}")
        println("余额: ${member.balance}")
        println("消费次数: ${member.consumerMembersNum}")
        println("充值次数: ${member.rechargeMembersNum}")
        
        // 示例：可能的业务操作
        // 1. 将会员信息填充到表单
        // 2. 跳转到会员详情页面
        // 3. 进行支付操作
        // 4. 更新订单的会员信息
        // 等等...
    }

    /**
     * 在支付场景中使用会员搜索
     * 
     * @param fragmentManager Fragment管理器
     * @param onMemberSelected 会员选择回调
     */
    fun showForPayment(
        fragmentManager: FragmentManager,
        onMemberSelected: (Record) -> Unit
    ) {
        SearchMemberDialog.showDialog(fragmentManager) { selectedMember ->
            // 检查会员余额是否足够
            val balance = selectedMember.balance ?: 0L
            if (balance > 0) {
                onMemberSelected(selectedMember)
            } else {
                // 余额不足，可以提示用户充值
                println("会员余额不足，请先充值")
            }
        }
    }

    /**
     * 在会员管理场景中使用会员搜索
     * 
     * @param fragmentManager Fragment管理器
     * @param onMemberSelected 会员选择回调
     */
    fun showForMemberManagement(
        fragmentManager: FragmentManager,
        onMemberSelected: (Record) -> Unit
    ) {
        SearchMemberDialog.showDialog(fragmentManager) { selectedMember ->
            // 直接返回选中的会员，用于编辑或查看详情
            onMemberSelected(selectedMember)
        }
    }
}
