package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.DialogSearchMemberBinding
import com.metathought.food_order.casheir.ui.dialog.adapter.MemberSearchAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SearchMemberDialog : BaseDialogFragment() {

    private var binding: DialogSearchMemberBinding? = null
    private val viewModel: SearchMemberViewModel by viewModels()
    private lateinit var memberAdapter: MemberSearchAdapter

    // 回调接口
    private var onMemberSelectedListener: ((Record) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSearchMemberBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        setupRecyclerView()
        setupSearchView()
        observeViewModel()

        // 初始加载所有会员
        viewModel.searchMembers("")
    }




    companion object {
        private const val TAG = "SearchMemberDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentSearchMemberDialog(fragmentManager: FragmentManager): SearchMemberDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            fragment?.dismissAllowingStateLoss()
        }


        private fun newInstance(
//            orderNo: String?,
//            title: String? = null,
//            isShowAutoInStore: Boolean? = false,
//            positiveButtonListener: ((String, Boolean) -> Unit),
        ): SearchMemberDialog {
            val args = Bundle()

            val fragment = SearchMemberDialog()
//
//            args.putBoolean(IS_SHOW_AUTO_IN_STORE, isShowAutoInStore ?: false)
//
//            args.putString(ORDER_NO, orderNo)
//            args.putString(TITLE, title)
//            fragment.arguments = args
//            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }

}