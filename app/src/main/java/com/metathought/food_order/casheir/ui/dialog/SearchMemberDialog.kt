package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.metathought.food_order.casheir.databinding.DialogSearchMemberBinding
import com.metathought.food_order.casheir.ui.dialog.adapter.MemberSearchAdapter
import com.metathought.food_order.casheir.ui.dialog.decoration.MemberItemDecoration
import com.metathought.food_order.casheir.extension.toDp
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SearchMemberDialog : BaseDialogFragment() {

    private var binding: DialogSearchMemberBinding? = null
    private val viewModel: SearchMemberViewModel by viewModels()
    private lateinit var memberAdapter: MemberSearchAdapter

    // 回调接口
    private var onMemberSelectedListener: ((CustomerMemberResponse) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSearchMemberBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        setupRecyclerView()
        setupSearchView()
        setupRefreshLayout()
        observeViewModel()

        // 初始加载所有会员
        viewModel.searchMembers("")
    }


    private fun setupRecyclerView() {
        memberAdapter = MemberSearchAdapter { member ->
            onMemberSelectedListener?.invoke(member)
            dismissAllowingStateLoss()
        }

        binding?.recyclerViewMembers?.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = memberAdapter

            // 添加间距装饰器
            val spacing = 10.toDp(resources).toInt()
            addItemDecoration(MemberItemDecoration(spacing))
        }
    }

    private fun setupRefreshLayout() {
        binding?.refreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                val keyword = binding?.searchView?.getSearchContent()
                viewModel.searchMembers(keyword, isRefresh = true)
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                viewModel.loadMoreMembers()
            }
        })
    }

    private fun setupSearchView() {
        binding?.searchView?.apply {
            setTextChangedListenerCallBack {
                val keyword = getSearchContent()
                viewModel.searchMembersWithDebounce(keyword)
            }

            getEditText()?.setOnEditorActionListener { _, _, _ ->
                val keyword = getSearchContent()
                viewModel.searchMembers(keyword)
                removeFocus()
                true
            }
        }
    }

    private fun observeViewModel() {
        viewModel.uiState.observe(viewLifecycleOwner) { uiModel ->
            uiModel?.let { handleUiState(it) }
        }
    }

    private fun handleUiState(uiModel: SearchMemberViewModel.UIModel) {
        // 处理加载状态
        binding?.apply {
            if (uiModel.showLoading == true) {
                if (memberAdapter.itemCount == 0) {
                    progressBar?.isVisible = true
                }
            } else {
                progressBar?.isVisible = false
                // 停止刷新动画
                refreshLayout?.finishRefresh()
                refreshLayout?.finishLoadMore()
            }
        }

        // 处理错误状态
        uiModel.showError?.let { error ->
            Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            binding?.apply {
                refreshLayout?.finishRefresh()
                refreshLayout?.finishLoadMore()
            }
        }

        // 处理会员列表数据
        uiModel.memberList?.let { memberList ->
            if (uiModel.isRefresh == true) {
                memberAdapter.replaceData(memberList)
            } else {
                memberAdapter.addData(memberList)
            }

            // 设置是否还有更多数据
            binding?.refreshLayout?.setEnableLoadMore(uiModel.hasMoreData == true)

            // 显示空状态
            binding?.apply {
                val isEmpty = memberAdapter.itemCount == 0
                textViewEmpty?.isVisible = isEmpty
                recyclerViewMembers?.isVisible = !isEmpty

                if (isEmpty) {
                    textViewEmpty?.text =
                        if (searchView?.getSearchContent()?.isNotEmpty() == true) {
                            getString(R.string.no_member_found)
                        } else {
                            getString(R.string.no_members)
                        }
                }
            }
        }
    }

    /**
     * 设置会员选择监听器
     */
    fun setOnMemberSelectedListener(listener: (CustomerMemberResponse) -> Unit) {
        onMemberSelectedListener = listener
    }

    companion object {
        private const val TAG = "SearchMemberDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            onMemberSelected: ((CustomerMemberResponse) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            onMemberSelected?.let { fragment.setOnMemberSelectedListener(it) }
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentSearchMemberDialog(fragmentManager: FragmentManager): SearchMemberDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(): SearchMemberDialog {
            return SearchMemberDialog()
        }
    }

}