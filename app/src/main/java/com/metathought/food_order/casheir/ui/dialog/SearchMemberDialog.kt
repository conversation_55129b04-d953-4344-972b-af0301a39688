package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.metathought.food_order.casheir.databinding.DialogSearchMemberBinding
import com.metathought.food_order.casheir.ui.dialog.adapter.MemberSearchAdapter
import com.metathought.food_order.casheir.ui.dialog.decoration.MemberItemDecoration
import com.metathought.food_order.casheir.extension.toDp
import com.metathought.food_order.casheir.extension.hideKeyboard2
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SearchMemberDialog : BaseDialogFragment() {

    private var binding: DialogSearchMemberBinding? = null
    private val viewModel: SearchMemberViewModel by viewModels()
    private lateinit var memberAdapter: MemberSearchAdapter

    // 回调接口
    private var onMemberSelectedListener: ((CustomerMemberResponse) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        binding = DialogSearchMemberBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initListener()
        setupRecyclerView()
        setupSearchView()
        setupRefreshLayout()
        observeViewModel()

        // 初始化显示空位图
        initializeEmptyState()

        // 延迟自动获取焦点，确保对话框完全显示后再获取焦点
        Handler(Looper.getMainLooper()).postDelayed({
            autoFocusSearchView()
        }, 200)


    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }
        }
    }


    private fun setupRecyclerView() {
        memberAdapter = MemberSearchAdapter { member ->
            // 选择会员时先关闭键盘
            hideKeyboard2()
            onMemberSelectedListener?.invoke(member)
            dismissAllowingStateLoss()
        }

        binding?.recyclerViewMembers?.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = memberAdapter

            // 添加间距装饰器
            val spacing = 10.toDp(resources).toInt()
            addItemDecoration(MemberItemDecoration(spacing))
        }
    }

    private fun setupRefreshLayout() {
        binding?.refreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                val keyword = binding?.searchView?.getSearchContent()

                if (keyword.isNullOrEmpty()) {
                    // 输入框为空时，直接清空列表并停止刷新
                    clearMemberList()
                } else {
                    // 有搜索内容时才请求接口
                    viewModel.searchMembers(keyword, isRefresh = true)
                }
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                // 只有在有搜索内容时才允许加载更多
                val keyword = binding?.searchView?.getSearchContent()
                if (!keyword.isNullOrEmpty()) {
                    viewModel.loadMoreMembers()
                } else {
                    // 没有搜索内容时直接完成加载更多
                    refreshLayout.finishLoadMore()
                }
            }
        })
    }

    private fun setupSearchView() {
        binding?.searchView?.apply {
            setTextChangedListenerCallBack {
                val keyword = getSearchContent()

                if (keyword.isNullOrEmpty()) {
                    // 输入框为空时，直接清空列表数据，不请求接口
                    clearMemberList()
                } else {
                    // 有搜索内容时才请求接口
                    viewModel.searchMembersWithDebounce(keyword)
                }

                // 当搜索内容变化时，如果当前显示空状态，更新空状态布局
                if (binding?.layoutEmptyData?.root?.isVisible == true) {
                    updateEmptyDataLayout()
                }
            }

            getEditText()?.setOnEditorActionListener { _, actionId, _ ->
                if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH || actionId == android.view.inputmethod.EditorInfo.IME_ACTION_DONE) {
                    val keyword = getSearchContent()

                    if (keyword.isNullOrEmpty()) {
                        // 输入框为空时，直接清空列表
                        clearMemberList()
                    } else {
                        // 有搜索内容时才请求接口
                        viewModel.searchMembers(keyword)
                    }

                    removeFocus()
                    // 隐藏键盘
                    hideKeyboard2()
                    true
                } else {
                    false
                }
            }
        }
    }

    private fun clearMemberList() {
        // 清空适配器数据
        memberAdapter.clearData()

        // 显示空状态
        binding?.apply {
            layoutEmptyData?.root?.isVisible = true
            recyclerViewMembers?.isVisible = false
            progressBar?.isVisible = false

            // 禁用上拉加载
            refreshLayout?.setEnableLoadMore(false)

            // 停止刷新动画（如果正在刷新）
            refreshLayout?.finishRefresh()
            refreshLayout?.finishLoadMore()
        }

        // 更新空状态布局显示
        updateEmptyDataLayout()
    }

    private fun initializeEmptyState() {
        binding?.apply {
            // 初始化时显示空位图，隐藏 RecyclerView
            layoutEmptyData?.root?.isVisible = true
            recyclerViewMembers?.isVisible = false
            progressBar?.isVisible = false

            // 更新空位图内容
            updateEmptyDataLayout()
        }
    }

    private fun autoFocusSearchView() {
        binding?.searchView?.getEditText()?.apply {
            // 请求焦点
            requestFocus()
            requestFocusFromTouch()

            // 显示键盘
            post {
                val inputMethodManager =
                    context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                inputMethodManager.showSoftInput(
                    this, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT
                )
            }
        }
    }

    private fun observeViewModel() {
        viewModel.uiState.observe(viewLifecycleOwner) { uiModel ->
            uiModel?.let { handleUiState(it) }
        }
    }

    private fun handleUiState(uiModel: SearchMemberViewModel.UIModel) {
        // 处理加载状态
        binding?.apply {
            if (uiModel.showLoading == true) {
                if (memberAdapter.itemCount == 0) {
                    progressBar?.isVisible = true
                }
            } else {
                progressBar?.isVisible = false
                // 停止刷新动画
                refreshLayout?.finishRefresh()
                refreshLayout?.finishLoadMore()
            }
        }

        // 处理错误状态
        uiModel.showError?.let { error ->
            Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            binding?.apply {
                refreshLayout?.finishRefresh()
                refreshLayout?.finishLoadMore()
            }
        }

        // 处理会员列表数据
        uiModel.memberList?.let { memberList ->
            android.util.Log.d("SearchMember", "处理会员列表数据: 数量=${memberList.size}, isRefresh=${uiModel.isRefresh}")

            if (uiModel.isRefresh == true) {
                android.util.Log.d("SearchMember", "替换数据: ${memberList.size} 条记录")
                memberAdapter.replaceData(memberList)
            } else {
                android.util.Log.d("SearchMember", "追加数据: ${memberList.size} 条记录")
                memberAdapter.addData(memberList)
            }

            android.util.Log.d("SearchMember", "适配器更新后数量: ${memberAdapter.itemCount}")

            // 设置是否还有更多数据
            binding?.refreshLayout?.setEnableLoadMore(uiModel.hasMoreData == true)

            // 显示空状态 - 当页码为1且搜索结果为空数组时显示空位图
            binding?.apply {
                val isEmpty = memberAdapter.itemCount == 0
                val isFirstPageEmpty = uiModel.isFirstPage == true && memberList.isEmpty()

                if (isEmpty || isFirstPageEmpty) {
                    layoutEmptyData?.root?.isVisible = true
                    recyclerViewMembers?.isVisible = false
                    updateEmptyDataLayout()
                } else {
                    layoutEmptyData?.root?.isVisible = false
                    recyclerViewMembers?.isVisible = true
                }
            }
        }
    }

    /**
     * 更新空数据布局的显示内容
     */
    private fun updateEmptyDataLayout() {
        binding?.layoutEmptyData?.root?.let { emptyLayout ->
            val emptyTextView = emptyLayout.findViewById<TextView>(R.id.tvEmptyText)
            val emptyImageView = emptyLayout.findViewById<ImageView>(R.id.imgError)

            val searchContent = binding?.searchView?.getSearchContent()

            if (searchContent.isNullOrEmpty()) {
                // 输入框没内容时的提示
                emptyTextView?.text = getString(R.string.search_member_empty_hint)
                emptyImageView?.setImageResource(R.drawable.ic_search_empty)
            } else {
                // 搜索内容为空时的提示
                emptyTextView?.text = getString(R.string.no_data_available)
                emptyImageView?.setImageResource(R.drawable.ic_empty_box)
            }
        }
    }

    /**
     * 设置会员选择监听器
     */
    fun setOnMemberSelectedListener(listener: (CustomerMemberResponse) -> Unit) {
        onMemberSelectedListener = listener
    }

    override fun onDestroyView() {
        // 确保键盘关闭
        hideKeyboard2()
        super.onDestroyView()
        binding = null
    }

    override fun dismiss() {
        // 关闭对话框时确保键盘关闭
        hideKeyboard2()
        super.dismiss()
    }

    override fun dismissAllowingStateLoss() {
        // 关闭对话框时确保键盘关闭
        hideKeyboard2()
        super.dismissAllowingStateLoss()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.45).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    companion object {
        private const val TAG = "SearchMemberDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            onMemberSelected: ((CustomerMemberResponse) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            onMemberSelected?.let { fragment.setOnMemberSelectedListener(it) }
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentSearchMemberDialog(fragmentManager: FragmentManager): SearchMemberDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(): SearchMemberDialog {
            return SearchMemberDialog()
        }
    }

}