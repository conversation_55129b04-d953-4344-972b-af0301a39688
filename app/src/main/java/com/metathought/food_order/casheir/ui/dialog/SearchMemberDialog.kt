package com.metathought.food_order.casheir.ui.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogCancelCreditBinding
import com.metathought.food_order.casheir.databinding.DialogSearchMemberBinding

class SearchMemberDialog : BaseDialogFragment() {

    private var binding: DialogSearchMemberBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSearchMemberBinding.inflate(layoutInflater)
        return binding?.root
    }





    companion object {
        private const val TAG = "SearchMemberDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentSearchMemberDialog(fragmentManager: FragmentManager): SearchMemberDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SearchMemberDialog
            fragment?.dismissAllowingStateLoss()
        }


        private fun newInstance(
//            orderNo: String?,
//            title: String? = null,
//            isShowAutoInStore: Boolean? = false,
//            positiveButtonListener: ((String, Boolean) -> Unit),
        ): SearchMemberDialog {
            val args = Bundle()

            val fragment = SearchMemberDialog()
//
//            args.putBoolean(IS_SHOW_AUTO_IN_STORE, isShowAutoInStore ?: false)
//
//            args.putString(ORDER_NO, orderNo)
//            args.putString(TITLE, title)
//            fragment.arguments = args
//            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }

}