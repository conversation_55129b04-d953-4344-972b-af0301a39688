package com.metathought.food_order.casheir.ui.dialog.notice

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MultipleOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import retrofit2.http.Path
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class NoticeViewModel @Inject
constructor(val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIListModel>()
    private val _detailState = MutableLiveData<ApiResponse<NoticeResponse>>()
    val uiState get() = _uiState
    val detailState get() = _detailState
    private var pageNo = 1
    private var pageSize = 20
    fun getNoticeList(isRefresh: Boolean) {
        viewModelScope.launch {
            if (isRefresh) {
                pageNo = 1
                if (_uiState.value?.showLoading == true) {
                    //防止刷新的请求太多次
                    return@launch
                }
                if (isRefresh == null) {
                    emitUiState(showLoading = true)
                }

            }

            try {
                val response = repository.getNoticeList(pageNo, pageSize)
                if (response is ApiResponse.Success) {
                    val result = response.data
                    if (result.records.isNullOrEmpty()) {
                        emitUiState(
                            showEnd = true,
                            isRefresh = isRefresh,
                            showLoading = false
                        )
                        return@launch
                    }
                    pageNo++
                    emitUiState(
                        showSuccess = response.data,
                        isRefresh = isRefresh,
                        showLoading = false,
                    )
                } else if (response is ApiResponse.Error) {
                    emitUiState(showError = response.message, showLoading = false)
                }

            } catch (e: Exception) {
                emitUiState(showError = e.message, showLoading = false)
            }
        }
    }

    /**
     * 已读通知
     *
     */
    fun getNotice(notice: NoticeResponse) {
        viewModelScope.launch {
            try {

                val response = repository.getNoticeDetail(notice.id!!.toString(), true)
                if (response is ApiResponse.Success) {
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_NOTICE_UNREAD_EVENT, response.data))
                }
                detailState.postValue(response)
            } catch (e: Exception) {

            }
        }
    }


    private fun emitUiState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: NoticeListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel = UIListModel(showLoading, showError, showSuccess, showEnd, isRefresh)
        _uiState.postValue(uiModel)
    }

    //    data class UIModel(
//        val response: ApiResponse<NoticeListResponse>?,
//    )
    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: NoticeListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

}