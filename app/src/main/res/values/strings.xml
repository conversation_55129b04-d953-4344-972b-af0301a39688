<resources>
    <string name="english_language" translatable="false">English</string>
    <string name="khmer_language" translatable="false">ខ្មែរ</string>
    <string name="chinese_language" translatable="false">中文</string>
    <!--    <string name="app_name">MPOS Cashier System</string>-->
    <string name="button_login">Login</string>
    <string name="hint_user_name">Login Account  <font color='#FF0000'>*</font></string>
    <string name="hint_password">Login Password <font color='#FF0000'>*</font></string>
    <string name="brand_name">山河茶餐厅</string>
    <string name="table">Table</string>
    <string name="order">Menu</string>
    <string name="pending_orders">Pending Orders</string>
    <string name="order_management">Orders</string>
    <string name="order_management1">Orders</string>
    <string name="dashboard">Dashboard</string>
    <string name="staff_name">Staff Name</string>
    <string name="log_out">Logout</string>
    <string name="login_time">Login Time</string>
    <string name="total_orders">Total Orders</string>
    <string name="amount_of_cash_handed_over">Amount of Cash handed over <font color='#FF0000'>*</font></string>
    <string name="required" translatable="false">*</string>
    <string name="unpaid_orders">Unpaid Orders</string>
    <string name="input">Input</string>
    <string name="filter_all">All (%1$d)</string>
    <string name="all">All</string>
    <string name="filter_available">Available (%1$d)</string>
    <string name="filter_reserved">To be paid (%1$d)</string>
    <string name="filter_dining">Dining (%1$d)</string>
    <string name="available">Available</string>
    <string name="reserved">Reserved</string>
    <string name="dining">Dining</string>
    <string name="take_away">Take-away</string>
    <string name="dine_in">Dine-in</string>
    <string name="not_selected">Not Selected</string>
    <string name="customer_info">Customer Info</string>
    <string name="customer_name">Customer Name</string>
    <string name="phone_number">Phone Number</string>
    <string name="people">People</string>
    <string name="dining_time">Dining-Time</string>
    <string name="items">Items</string>
    <string name="quantity">Quantity</string>
    <string name="amount">Amount</string>
    <string name="order_pending_search_hint">Table number, order …</string>
    <string name="pending_order_info">Pending Order Information</string>
    <string name="pending_order_id">Pending Order ID</string>
    <string name="pending_order_time">Pending Order Time</string>
    <string name="remark">Remark</string>
    <string name="resume_order">Resume Order</string>
    <string name="total">Total</string>
    <string name="discounted">Discount</string>
    <string name="vat">VAT</string>
    <string name="subtotal">SubTotal</string>
    <string name="confirmed">Confirmed</string>
    <string name="ordering_time">Order Time</string>
    <string name="order_id">Order ID</string>
    <string name="order_more">Order More</string>
    <string name="pay">Pay</string>
    <string name="order_info">Order Information</string>
    <string name="ordered_time">Order Time</string>
    <string name="order_type">Order Type</string>
    <string name="order_by">Order Source</string>
    <string name="balance">Balance</string>
    <string name="cash">Cash</string>
    <string name="online">Online</string>
    <string name="payment_nmethod">Payment\nMethod</string>
    <string name="cancel_reservation">Cancel Reservation</string>
    <string name="cancel_reservation_question">Cancel Reservation?</string>
    <string name="no">No</string>
    <string name="yes_cancel">Yes, Cancel</string>
    <string name="reserve">Placeholder</string>
    <string name="hint_dining_time_require">Dining Time <font color='#FF0000'>*</font></string>
    <string name="hint_number_of_people_required">Number Of Pax <font color='#FF0000'>*</font></string>
    <string name="hint_customer_name_required">Customer Name <font color='#FF0000'>*</font></string>
    <string name="hint_phone_number_required">Phone Number <font color='#FF0000'>*</font></string>
    <string name="clear_table">Clear Table</string>
    <string name="view_ordered">View Ordered</string>
    <string name="clear_table_confrim_question">Clear Table?</string>
    <string name="clear">Clear</string>
    <string name="cancel">Cancel</string>
    <string name="search_table_customer_phone_number">Search Table Name/Customer/Phone No.</string>
    <string name="your_password_is_incorrect_please_try_again">Your password is incorrect, Please try again</string>
    <string name="your_username_is_incorrect_please_try_again">Your username is incorrect, Please try again</string>
    <string name="select_table">Select Table</string>
    <string name="item_detail">Item Detail</string>
    <string name="add">Add</string>
    <string name="overview">Overview</string>
    <string name="search_name">Search name</string>
    <string name="yesterday">Yesterday</string>
    <string name="top_up_amount">Top Up Amount</string>
    <string name="top_up_amount_require">Top Up Amount <font color='#FF0000'>*</font></string>
    <string name="paid_amount_of_members">Paid Amount</string>
    <string name="number_of_paid_member">Consumption Customer Numbers</string>
    <string name="line_chart_of_daily_top_up_amount">Line Chart of Top Up Amount</string>
    <string name="unit_usd">Unit：USD</string>
    <string name="profile">Profile</string>
    <string name="account_name">Account Name</string>
    <string name="account_number">Account Number</string>
    <string name="register_date">Register Date</string>
    <string name="last_top_up_amount">Last Top-up Date</string>
    <string name="operation">Operation</string>
    <string name="start_date_end_date">Start Date - End Date</string>
    <string name="type">Type</string>
    <string name="recharge_amount">Top-up Amount</string>
    <string name="status">Status</string>
    <string name="revenue_amount">Revenue Amount</string>
    <string name="view_order_detail">View Order Detail</string>
    <string name="online_payment">Online Payment</string>
    <string name="online_payment_title">Online Payment:</string>
    <string name="cash_chart_titile">Cash:</string>
    <string name="balance_chart_title">Balance:</string>
    <string name="top_sales_list">Top Sales List</string>
    <string name="date">Date</string>
    <string name="actual_revenue">Actual Revenue</string>
    <string name="paid_orders">Paid Orders</string>
    <string name="refund_orders">Refund Orders</string>
    <string name="pay_now">Pay</string>
    <string name="search_product">Search Item</string>
    <string name="ok">OK</string>
    <string name="dining_time_in_mins">%1$d mins</string>
    <string name="pre_order">Pre-order</string>
    <string name="toppings">Toppings</string>
    <string name="confirm">Confirm</string>
    <string name="make_payment_first">Make Payment First</string>
    <string name="payment">Payment</string>
    <string name="order_pending_empty">Order Pending Empty</string>
    <string name="empty_data">Empty Data</string>
    <string name="choose_payment">Choose Payment Method <font color='#FF0000'>*</font></string>
    <string name="remove">Remove</string>
    <string name="remove_num_items">Remove %1$d items?</string>
    <string name="pending">Pending</string>
    <string name="pay_by_cash">Cash Payment</string>
    <string name="pay_by_balance">Balance Payment </string>
    <string name="balance_info">Balance Info</string>
    <string name="total_with_colon">Total:</string>
    <string name="my_balance_with_colon">My Balance:</string>
    <string name="member_name_with_colon">Member Name:</string>
    <string name="user_not_found">Customer Not Found</string>
    <string name="insufficient_balance">Insufficient Balance</string>
    <string name="top_up">Top Up</string>
    <string name="qa_pay_by_cash">Pay by cash?</string>
    <string name="staff_info">Staff Info</string>
    <string name="amount_require">Amount <font color='#FF0000'>*</font></string>
    <string name="confirmation">Confirm</string>
    <string name="payment_successfully">Payment Successfully</string>
    <string name="please_select_item">Please Select Item(s)</string>
    <string name="partial_refund">Partial Refund</string>
    <string name="refunded">Refund</string>
    <string name="order_cancel">Cancelled</string>
    <string name="no_result_found">No result found</string>
    <string name="unpaid">Unpaid</string>
    <string name="paid">Paid</string>
    <string name="all_order">All Order</string>
    <string name="all_table">All Table</string>
    <string name="filter_by_table_name">Filter by Table Name</string>
    <string name="reset">Reset</string>
    <string name="apply_filter">Apply Filter</string>
    <string name="clear_filter">Clear Filter</string>
    <string name="request_refund">Refund</string>
    <string name="refund_Type">Refund Type <font color='#FF0000'>*</font></string>
    <string name="order_expired">The Order Has Expired</string>
    <string name="full_refund">Full Refund</string>
    <string name="refund_quantity">Refund Qty</string>
    <string name="refund_vat">VAT Refund</string>
    <string name="confirm_refund">Confirm Refund</string>
    <string name="refund">Refund</string>
    <string name="refund_amount_confirm">Refund %1$s?</string>
    <string name="partial_refund_amount">Partial Refund Amount</string>
    <string name="vat_refund">VAT Refund</string>
    <string name="actual_received_amount">Actual Received Amount</string>
    <string name="ordered_dining_time">Dining Time</string>
    <string name="payment_time">Payment Time</string>
    <string name="payment_method">Payment Method</string>
    <string name="refund_amount">Refund Amount</string>
    <string name="clear_pending_order_confimation_msg">Clear Pending Order?</string>
    <string name="none">None</string>
    <string name="clear_items_in_cart_first_to_resume_this_order_confrimation_msg">There are Items in cart, please process them first</string>
    <string name="go_to_menu">Go to Menu</string>
    <string name="refund_time">Refund Time</string>
    <string name="refund_qty">Refund QTY</string>
    <string name="please_input_customer_information">Please input customer information</string>
    <string name="please_select_a_table_number">Please Select a Table</string>
    <string name="hint_dining_time">Dining Time</string>
    <string name="hint_number_of_people">Number of People</string>
    <string name="hint_customer_name">Customer Name</string>
    <string name="hint_phone_number">Phone Number</string>
    <string name="manual_printing">Manual Printing</string>
    <string name="kitchen_receipt">Kitchen Receipt</string>
    <string name="cashier_receipt">Dine-in Receipt</string>
    <string name="choose_type_of_receipt">Choose Type of Receipt</string>
    <string name="close">Close</string>
    <string name="old_order_items">Previous Ordered (%1$d items)</string>
    <string name="new_order">Order More (%1$d items)</string>
    <string name="common_table">Common Table</string>
    <string name="kiosk">Kiosk</string>
    <string name="employee_terminal">Employee Terminal</string>
    <string name="h5_user">H5 User</string>
    <string name="cashier">Cashier</string>
    <string name="unknown">Unknown</string>
    <string name="order_table">Order</string>
    <string name="series_number">Series Number</string>
    <string name="account_balance">Account Balance</string>
    <string name="extra_bonus">Extra Bonus</string>
    <string name="success">Success</string>
    <string name="fail">Fail</string>
    <string name="it_looks_like_you_ve_been_away_for_a_while_please_login_again">It looks like you\'ve been away for a while.\nPlease login again</string>
    <string name="back_to_login">Back to Login</string>
    <string name="session_expired">Session Expired</string>
    <string name="top_up_successfully">Top-Up Successfully</string>
    <string name="today">Today</string>
    <string name="this_week">This Week</string>
    <string name="this_month">This Month</string>
    <string name="this_quarter">This Quarter</string>
    <string name="this_year">This Year</string>
    <string name="pending_success">The pending order was successful.</string>
    <string name="unpaid_amount">Unpaid Amount</string>
    <string name="unsettled_amount">Unsettled Amount</string>
    <string name="settled_amount">Settled Amount</string>
    <string name="order_numbers">Total Orders</string>
    <string name="n_a" translatable="false">N/A</string>
    <string name="detail">Detail</string>
    <string name="all_payment_method">All Payment Method</string>
    <string name="orders_list">Orders List</string>
    <string name="already_goods_shopping_cart_tip">There is item in cart, please process first to Order More.</string>
    <string name="refund_successfully">Refund successful</string>
    <string name="power_by_mpos">Powered by MPOS F&amp;B Management</string>
    <string name="ordered_confirmed">Confirmed</string>
    <string name="number_of_top_up_member">Top-up Customer Numbers</string>
    <string name="number_of_members">Customer Numbers</string>
    <string name="paid_amount">Paid Amount</string>
    <string name="unit_people">Unit：People</string>
    <string name="line_chart_of_number_of_top_up_member">Line chart of top-up customer numbers</string>
    <string name="this_order_has_been_printed_many_times_please_be_careful_to_avoid_repeated_printing">This order has been printed many times, please be careful to avoid repeated printing.</string>
    <string name="order_status">Order Status</string>
    <string name="please_select_table_to_process_next">Please select table to process next</string>
    <string name="no_available_table_currently">No available table currently</string>
    <string name="default_data_showing" translatable="false">--</string>
    <string name="action">Action</string>
    <string name="cancel_time">Cancel Time</string>
    <string name="invalid_format_enter_8_to_12_digits">Invalid format. Enter 8 to 12 digits.</string>
    <string name="exceeded_amount_maximum_top_up_of_500_allowed">Exceeded amount. Maximum top-up of $99999.99 allowed</string>
    <string name="pending_order">Pending Order</string>
    <string name="maximum_100_characters_allowed">Maximum 100 characters allowed</string>
    <string name="go_back">Go Back</string>
    <string name="sure_to_cancel_this_top_up">Sure To Cancel This Top Up?</string>
    <string name="no_chart_data_available">No chart data available</string>
    <string name="top_up_must_be_greater_than_5">Top-up must be greater than 5</string>
    <string name="no_internet_connection">No Internet Connection</string>
    <string name="time">Time </string>
    <string name="error_title_service_unavailable">Service Unavailable</string>
    <string name="you_have_reached_the_maximum_quantity_limit_of">You have reached the maximum quantity limit of %1$d</string>
    <string name="login_information_expired_please_login_again">Login information expired. please login again.</string>
    <string name="to_be_confirmed">Unconfirmed</string>
    <string name="change_payment">Change Payment</string>
    <string name="order_id_table_phone_number">Order ID/Table/Phone Number</string>
    <string name="offline_payments">Offline Payment</string>
    <string name="select_offline_payment_channel">Please select an offline payment channel</string>
    <string name="back_your_change_amount">Change</string>
    <string name="cash_collection">Received</string>
    <string name="amount_receivable">Amount to Receive</string>
    <string name="conversion_ratio">Conversion Rate</string>
    <string name="order_amount">Order Amount</string>
    <string name="insufficient_cash_received">Not enough cash receive</string>
    <string name="cancel_dish">Return Item</string>
    <string name="cancel_order">Cancel Order</string>
    <string name="sure_to_cancel_order">Confirm to cancel this order?</string>
    <string name="deny">No</string>
    <string name="confirm_cancel_dish">Confirm withdraw</string>
    <string name="cancel_dish_num">Quantity of withdraw item</string>
    <string name="cancel_dish_amount">Withdraw Amount</string>
    <string name="cancel_dish_vat">Additional Tax Refund </string>
    <string name="cancel_dish_packing_charge">Packaging Fee Refund </string>
    <string name="print_temporary_table_code">Print Temporary Table Code</string>
    <string name="your_best_business_partner">Your Best Business Partner</string>
    <string name="welcome">Welcome</string>
    <string name="plz_scan_qr_pay_before_expires">Please scan the QR code to pay before it\'s expired</string>
    <string name="table_name">Table Name</string>
    <string name="number_products">Number of Item(s)</string>
    <string name="discount_reduction">Discount</string>
    <string name="receivable">To be Received</string>
    <string name="vip_price">Member’s Price</string>
    <string name="packing_price">Packaging Fee</string>
    <string name="total_price">Grand Total</string>
    <string name="total_price2">Total Price</string>
    <string name="packing_price_detail">Packaging Fee Details</string>
    <string name="to_be_weighed">Unweighted</string>
    <string name="cancel_dish_total_price">Total Withdraw Amount</string>
    <string name="refund_pack_price">Packaging Fee Refund</string>
    <string name="refund_total_price">Total Refund Amount</string>
    <string name="submit_order">Submit Order</string>
    <string name="please_input_product_weight">Please enter the item\'s weight</string>
    <string name="confirm2">Confirm</string>
    <string name="weight">Weight</string>
    <string name="single_price">Unit Price</string>
    <string name="confirm_order">Confirm Order</string>
    <string name="order_has_no_weighted_goods">The order contains weighable item, please weigh them before submitting.</string>
    <string name="return_back_dish_num">Withdraw Quantity</string>
    <string name="input_weight_rule_cue">Enter up to 3 integers and 3 decimals</string>
    <string name="change_table_cue">Change Table</string>
    <string name="change_table_to">The new replaced table is %s</string>
    <string name="change">Change</string>
    <string name="current_table_have_order_no_pay">Multiple unfinished orders at this table. Want to move all orders to new table?</string>
    <string name="current_table_have_current_order">There is a confirmed order at the target table. Want to cancel the origin order and finish the table change?</string>
    <string name="current_order">Current Order</string>
    <string name="agree">Confirm</string>
    <string name="network_timeout_try_again">The network connection has timed out, please try again later</string>
    <string name="refund_btn">Refund</string>
    <string name="offline_channel">线下支付-收款渠道</string>
    <string name="amount_payable_with_colon">Amounts payable:</string>
    <string name="offline">Offline</string>
    <string name="online_refund">Online Refund</string>
    <string name="offline_refund">Offline Refund</string>
    <string name="offline_payment_unit">Offline Payment:</string>
    <string name="there_are_unfinished_orders">There are unfinished orders, please process them first.</string>
    <string name="offline_payments_btn">Offline</string>
    <string name="online_payment_btn">Online</string>
    <string name="balance_payment_btn">Balance</string>
    <string name="offline_payments_btn_with_colon">Offline:</string>
    <string name="online_payment_btn_with_colon">Online：</string>
    <string name="goods_off_shelf">The product is no longer available. Please publish to order it.</string>
    <string name="srl_footer_finish" />
    <string name="refund_pack_fee">Pack Fee Refund</string>
    <string name="username_or_password_incorrect">Username or password is incorrect. Please try again</string>
    <string name="srl_footer_nothing">No More Data</string>
    <string name="srl_footer_loading">Loading…</string>
    <string name="connection_closed">Connection closed</string>
    <string name="recharge_times">Top Up Times</string>
    <string name="consumption_closed">Consumption Times</string>
    <string name="print_again">Print Again</string>
    <string name="print_title_datetime">Date</string>
    <string name="print_title_pre_order_time">Dining Time</string>
    <string name="print_title_receipt">Receipt</string>
    <string name="print_title_pre_settlement_bill">To Be Paid Receipt</string>
    <string name="print_title_pick_up_no">Pick-up No.</string>
    <string name="print_title_pack_fee">PACK FEE</string>
    <string name="print_title_paid_by_balance">Paid By Balance</string>
    <string name="print_title_order_no">Order No.</string>
    <string name="print_title_customer_name">Order Source</string>
    <string name="print_title_payment_time">PaymentTime</string>
    <string name="print_title_service_line">ServiceLine</string>
    <string name="print_title_remark">Remarks</string>
    <string name="print_title_pre_order">Pre-order</string>
    <string name="print_title_table_name">Table</string>
    <string name="print_title_total">Grand Total</string>
    <string name="print_title_received">Received</string>
    <string name="print_title_order_information">Order Information</string>
    <string name="print_title_change">Change</string>
    <string name="print_title_vat">VAT</string>
    <string name="print_title_subtotal">SubTotal</string>
    <string name="print_title_confirmed">Confirmed</string>
    <string name="print_title_paid">Paid</string>
    <string name="print_title_cook_bill">Cook Bill</string>
    <string name="print_title_item_name">Item</string>
    <string name="print_title_qty">Qty</string>
    <string name="print_title_u_price">Price</string>
    <string name="print_title_item_total">Total</string>
    <string name="print_title_discount_per">Discount</string>
    <string name="print_title_discount_amount">Discount</string>
    <string name="print_pay_by_cash">Cash</string>
    <string name="no_available_table_to_change">No available table to change currently</string>
    <string name="consumption">Consumption</string>
    <string name="print_title_company_name">CompanyName</string>
    <string name="print_title_vat_tin">VAT TIN</string>
    <string name="print_title_company_address">Address</string>
    <string name="print_title_contact_number">Contact</string>
    <string name="print_title_contact_email">Email</string>
    <string name="open_cashbox">Open Cashbox</string>
    <string name="update_content">Updated content:</string>
    <string name="update_now">Update Now</string>
    <string name="current_table_same_paple_have_order_no_pay">目标桌台存在相同顾客的待确认或已确认订单，是否将其取消并完成换桌</string>
    <string name="install_application_need_open_unkown_permission_tips">To install the app, enable permissions for unknown sources in settings</string>
    <string name="invalid_file">Invalid file</string>
    <string name="download_failed">Download failed</string>
    <string name="install">Install</string>
    <string name="downloading">Downloading…</string>
    <string name="discounts">Discount</string>
    <string name="selling_price">Sales Price</string>
    <string name="reduction_percentage">Discount(%)</string>
    <string name="discounts_percent">Discount(%s)</string>
    <string name="amount_of_reduction_usd">Discount Amount(USD)</string>
    <string name="amount_of_reduction_khr">Discount Amount(KHR)</string>
    <string name="do_not_remind_me_again">Don\'t remind again</string>
    <string name="version">Version</string>
    <string name="find_new_version">New version found</string>
    <string name="discount_input_khr_cue">Only positive integers multiple of 100 can be entered. Example: 100</string>
    <string name="discount_price_can_not_small_than_discount">Entered amount cannot be greater than the Amount to Receive</string>
    <string name="contact_us">Contact Us</string>
    <string name="contact_our_support">Contact Our Support</string>
    <string name="contact_us_cue">Click the button or scan the QR code to contact customer service</string>
    <string name="powered_by">Powered By x</string>
    <string name="click_here">click here</string>
    <string name="no_website">Your device doesn\'t have a web browser. Please install one to continue.</string>
    <string name="unread">Unread</string>
    <string name="unprint">Unprinted</string>
    <string name="please_input_greater_than_zero">Enter a number greater than 0</string>
    <string name="add_new_customer">New Customer</string>
    <string name="user_nickname">User\'s name <font color='#FF0000'>*</font></string>
    <string name="mobile_phone_number">Phone Number <font color='#FF0000'>*</font></string>
    <string name="save">Save</string>
    <string name="add_new">Add</string>
    <string name="print_scan_to_pay">Scan to Pay</string>
    <string name="has_need_weight_can_not_print">Please weigh the items before printing the To be Paid receipt.</string>
    <string name="discount_amount">Discount</string>
    <string name="service_fee">Service Fee</string>
    <string name="print_title_service_fee">Service Fee</string>
    <string name="refund_service_fee">Service Fee Refund</string>
    <string name="cancel_service_fee">Withdraw Service Fee</string>
    <string name="scan_to_pay">SCAN TO PAY</string>
    <string name="expired_in">Expired In</string>
    <string name="total_cost">TOTAL COST</string>
    <string name="service_fee_detail">Service Fee Details</string>
    <string name="service_phone">Customer Service: +855 1122 3328</string>
    <string name="cancel_reason">Cancelled Reason</string>
    <string name="hint_reason">Reason</string>
    <string name="review">View</string>
    <string name="contract">Fold</string>
    <string name="need_pay_for_service">Charge %s service fee</string>
    <string name="set_money_box">Cashbox Setting</string>
    <string name="cash_box_money">Cashbox passcode</string>
    <string name="reset_cash_box_pwd">Reset cashbox passcode</string>
    <string name="please_input_cash_box_pwd">Please enter cashbox passcode</string>
    <string name="current_login_account">Current login account</string>
    <string name="open_cash_box">Open cashbox</string>
    <string name="pwd_error_input_again">Incorrect passcode, please re-enter.</string>
    <string name="close_cash_box_pwd">Close cashbox passcode</string>
    <string name="account_error_input_again">Current login account is incorrect, please re-enter.</string>
    <string name="accounts_receivable">Accounts Receivable</string>
    <string name="back_to_menu">Go to Menu</string>
    <string name="remember_password">Remember Me</string>
    <string name="printer">Print</string>
    <string name="show_to_connect_wifi_printer">Sure want to reconnect the MPOS Wi-Fi printer?</string>
    <string name="m_pos_wifi_printer_colon">MPOS Wi-Fi printer: %s</string>
    <string name="connect">Connect</string>
    <string name="m_pos_wifi_printer">MPOS Wi-Fi printer</string>
    <string name="m_pos_usb_printer">MPOS USB printer</string>
    <string name="cook_bill">Kitchen Receipt</string>
    <string name="dine_in_bill">Dine-in Receipt</string>
    <string name="take_away_bill">Take-away Receipt</string>
    <string name="pre_order_bill">Pre-order Receipt</string>
    <string name="checkout_bill">Checkout Receipt</string>
    <string name="pre_checkout_bill">To Be Paid Receipt</string>
    <string name="wifi_printer_connect_error">%s connection failed. Please try again.</string>
    <string name="printer_name">Printer Name</string>
    <string name="printer_device">Printer Device</string>
    <string name="binding_time">Binding Time</string>
    <string name="ticket_scale">Receipt Size (MM)</string>
    <string name="template_configured">Configured Template</string>
    <string name="connection_status">Connection Status</string>
    <string name="operate">Operation</string>
    <string name="hint_printer_empty">No printer. Please bind a printer from Merchant BackOffice</string>
    <string name="no_time_to_print_pre_order">Printing time hasn\'t arrived yet. Please try again later.</string>
    <string name="print_title_invoiceNumber">Invoice Number</string>
    <string name="please_input_mark">Please enter your flavour, preferences and other requirements…</string>
    <string name="order_remark">Order Remark</string>
    <string name="customers_can_use_coupons">客户可用优惠券</string>
    <string name="hint_coupon_code">Coupon Code <font color='#FF0000'>*</font></string>
    <string name="identify_coupons">Identify coupon</string>
    <string name="this_order_offers_discounts">Discount:</string>
    <string name="gift_products_num">Gift items(%s)</string>
    <string name="can_not_use_reason">Unavailability reason</string>
    <string name="use_rule">Rules:</string>
    <string name="use_rule_colon">Rules</string>
    <string name="go_to_use">Use</string>
    <string name="gift_products">Gift items</string>
    <string name="coupon">Coupons</string>
    <string name="hint_no_can_use_coupon">No coupons available</string>
    <string name="hint_can_use_coupon">Coupons available</string>
    <string name="full_price_available">Spend %s to use</string>
    <string name="goods_num_available">Applicable item %s</string>
    <string name="goods_num_available_colon">Applicable item %s:</string>
    <string name="all_products">All items</string>
    <string name="scope_of_application">Applicable scope:</string>
    <string name="member_top_up">Member top-up</string>
    <string name="give_away_goods">Gift items</string>
    <string name="view_give_away_goods">View item</string>
    <string name="hint_user_coupon">Use Coupon</string>
    <string name="this_coupon_not_support">Coupon is not available</string>
    <string name="full_num_available">buy %s item to use</string>
    <string name="order_detail">Order Details</string>
    <string name="coupon_name">Coupon name</string>
    <string name="coupon_id">Coupon ID</string>
    <string name="coupon_reduce_price">Coupon discount amount</string>
    <string name="real_pay_price">Actual Paid</string>
    <string name="print_title_coupon">Coupon</string>
    <string name="free">Free</string>
    <string name="effect_time_to">Valid until %s</string>
    <string name="expires_in_a_few_days">Expires in %s days</string>
    <string name="expires_in_a_few_hour">Expires in %s hours</string>
    <string name="expires_in_a_few_minute">Expires in %s minutes</string>
    <string name="expires_in_a_few_second">Expires in %s seconds</string>
    <string name="expired">Expired</string>
    <string name="free_item_removed_by_merchant">Free item removed by merchant</string>
    <string name="no_available_items">No available items</string>
    <string name="receiving_orders">Accept Order</string>
    <string name="receiving_orders_auto_cancel_time">(Automatically cancel after %s)</string>
    <string name="please_input_num">Please enter quantity</string>
    <string name="wait_accept_order">To Be Accepted</string>
    <string name="has_accept_order">Order Accepted</string>
    <string name="count_down">Countdown %s</string>
    <string name="add_good_info">Order More Item</string>
    <string name="view_all">View All</string>
    <string name="no_accept_order">Decline</string>
    <string name="sure_no_accept_this_order">Sure not to accept this order?</string>
    <string name="add_goods_info_detail">Order More Details</string>
    <string name="add_goods_order">Order More Info </string>
    <string name="new_add_info">Order More %s</string>
    <string name="scan_to_topup">Scan to Top-up</string>
    <string name="max_num_to_add_cart">Able to order up to %s</string>
    <string name="pass_app_promo_code">PassApp Promo Code</string>
    <string name="automatically_store_returned_goods">Automatically inbound the returned items</string>
    <string name="manager_store_detail">Store Details</string>
    <string name="manager_store_name">Store Name</string>
    <string name="manager_store_name_required">Store Name <font color='#FF0000'>*</font></string>
    <string name="manager_store_phone">Phone Number</string>
    <string name="manager_store_phone_required">Phone Number <font color='#FF0000'>*</font></string>
    <string name="manager_store_location">Location</string>
    <string name="manager_store_location_required">Location <font color='#FF0000'>*</font></string>
    <string name="manager_store_intro">Store Introduction</string>
    <string name="manager_store_logo">LOGO Image</string>
    <string name="manager_store_logo_required">LOGO Image <font color='#FF0000'>*</font></string>
    <string name="manager_store_status">Status</string>
    <string name="manager_store_status_required">Status <font color='#FF0000'>*</font></string>
    <string name="manager_store_payment">Payment Method</string>
    <string name="manager_store_payment_required">Payment Method <font color='#FF0000'>*</font></string>
    <string name="manager_store_table_service">Sharing Table QR Code</string>
    <string name="manager_store_show_table">Display Table</string>
    <string name="manager_store_auto_accept">Automatic Accept Order</string>
    <string name="manager_store_casher_menu_show_image">Cashier Terminal displays the main image of the product</string>
    <string name="manager_store_ticket_need_meal_code">Receipt requires a Pick-up Number</string>
    <string name="manager_store_invoice_number">Invoice Number</string>
    <string name="manager_store_ticket_need_tax">Receipt requires Tax Information</string>
    <string name="manager_store_bind_upay">绑定U-Pay商户</string>
    <string name="manager_store_need_push">是否启用消息推送</string>
    <string name="manager_store_push_language">消息推送语言</string>
    <string name="manager_store_push_type">推送类型</string>
    <string name="edit">Edit</string>
    <string name="mult_language">Multi-language</string>
    <string name="upload_image_cue">Only png, jpg, jpeg files can be uploaded; file no more than 2MB</string>
    <string name="limit_custom_ding">Limit the distance between store and customers for place an order</string>
    <string name="manager_store_ticket_need_invoice_number">Receipt requires an Invoice Number</string>
    <string name="every_day_from">Every day from</string>
    <string name="loop">Cycle</string>
    <string name="start_required">Start <font color='#FF0000'>*</font></string>
    <string name="end_required">End <font color='#FF0000'>*</font></string>
    <string name="distance_required">Distance <font color='#FF0000'>*</font></string>
    <string name="set_location_distance_1">Customer and store need to be less than this distance to place orders for Dine-in and Take-away. </string>
    <string name="company_name">Company Name</string>
    <string name="company_vat_tin">VAT TIN</string>
    <string name="company_address">Address</string>
    <string name="company_phone">Contact</string>
    <string name="company_email">Email</string>
    <string name="prefix_required">Prefix</string>
    <string name="date_type">Date Type</string>
    <string name="starting_invoice_serial_number">Starting Invoice Serial Number <font color='#FF0000'>*</font></string>
    <string name="set_invoice_code_cue">Invoice Serial Numbers will be printed in order without interruption</string>
    <string name="khmer">Khmer</string>
    <string name="english">English</string>
    <string name="chinese">Chinese</string>
    <string name="done">Completed</string>
    <string name="normal_business_hours">Open</string>
    <string name="online_but_not_operating">Temperoliry Close</string>
    <string name="close_store">Close</string>
    <string name="pay_first">Pay First</string>
    <string name="post_pay">Post Payment</string>
    <string name="store_manager">Store Information</string>
    <string name="printer_manager">Printer Management</string>
    <string name="change_language">Swtich Language</string>
    <string name="announcement">Announcement</string>
    <string name="order_discount">Discount</string>
    <string name="weigh">Weight</string>
    <string name="anti_settlement">Reverse Checkout</string>
    <string name="re_weigh">Weigh Again</string>
    <string name="sure_to_anti_settlement">Sure to Reverse Checkout?</string>
    <string name="anti_settlement_reason_with_required">Reverse Checkout Reason <font color='#FF0000'>*</font></string>
    <string name="anti_settlement_reason">Reverse Checkout Reason</string>
    <string name="anti_settlement_reason_change_good">Change Item(s)</string>
    <string name="anti_settlement_reason_cancel_good_after_pay">Return Item After Done Payment</string>
    <string name="anti_settlement_reason_chang_payment">Change Payment Method</string>
    <string name="anti_settlement_reason_other">Others</string>
    <string name="please_input_anti_settlement_reason">Enter Reverse Checkout Reason</string>
    <string name="yes_desc">Yes</string>
    <string name="no_desc">No</string>
    <string name="metre">meter</string>
    <string name="the_closest_distance_is_50_meters">The nearest distance is 50 meters</string>
    <string name="please_input_store_name">Enter Store Name</string>
    <string name="please_input_store_phone">Enter Phone Number</string>
    <string name="please_input_store_location">Enter Location Details</string>
    <string name="please_input_invoice_number">Enter an Invoice Serial Number greater than 0</string>
    <string name="please_input_pickup_no_error">Only numbers can be entered; the number interval must be at least 20</string>
    <string name="please_input_pickup_no_error_2">Enter value from 001-999</string>
    <string name="save_success">Saved</string>
    <string name="consumption_details">Transaction Details</string>
    <string name="invoice_number_printing_format">Invoice Number printing format: %s</string>
    <string name="anti_settlement_success_cue">[Reverse checkout] This order was paid online. Please manually perform a [Refund] after reverse checkout.</string>
    <string name="sold_out">Sold Out</string>
    <string name="main_kitchen">Main kitchen</string>
    <string name="order_auto_cancel_time">The time of the order\'s automatic cancellation</string>
    <string name="accept_cancel_time">%1$d mins</string>
    <string name="accepted_payment">Accepted Payment</string>
    <string name="label_print">Label Printing</string>
    <string name="ticket">Receipt</string>
    <string name="label">Label</string>
    <string name="label_stickers">Label Sticker</string>
    <string name="merge_order">Merge Orders</string>
    <string name="split_order">Split Order</string>
    <string name="main_order">Main Order</string>
    <string name="mergeable_order">Mergeable Order</string>
    <string name="sure_to_merge_order">Sure to merge the order?</string>
    <string name="sure_to_splite_order">Sure to split the order?</string>
    <string name="order_index">Order %d</string>
    <string name="discount_activity">Promotion</string>
    <string name="no_goods">No items yet</string>
    <string name="no_mergeable_order">No Mergeable orders available</string>
    <string name="printer_type">Printer Type</string>
    <string name="print_title_order_type">Order Type</string>
    <string name="print_title_customer_type_type">Customer Type</string>
    <string name="print_title_Cashier">Cashier</string>
    <string name="print_title_number_of_pax">Number Of Pax</string>
    <string name="print_title_invoice">Invoice</string>
    <string name="print_title_to_be_paid">To Be Paid</string>
    <string name="print_title_usd_total_with_invoice">Total VAT Included</string>
    <string name="print_title_khr_total_with_invoice">Total Riel VAT Included</string>
    <string name="walk_in">Walk-in</string>
    <string name="print_title_ticket">Receipt</string>
    <string name="print_title_item_discount">Disc</string>
    <string name="print_title_item_index">No.</string>
    <string name="show_location_distance">Customers can place a dine-in or take-away order only if they are within %s meters from the store</string>
    <string name="longitude">Longitude</string>
    <string name="latitude">Latitude</string>
    <string name="please_input_lon_lat">Please enter the latitude and longitude first</string>
    <string name="print_report">Print Report</string>
    <string name="report_type">Report Type</string>
    <string name="report_type_required">Report Type <font color='#FF0000'>*</font></string>
    <string name="select_time_start_required">From Date-time</string>
    <string name="select_time_end_required">To Date-time</string>
    <string name="product_report">Product Report</string>
    <string name="payment_method_report">Payment Method Report</string>
    <string name="product_report_ticket">Item Suammry Receipt</string>
    <string name="payment_method_report_ticket">Payment Methods Report Receipt</string>
    <string name="print_time">Printed Time</string>
    <string name="print_product_info">Item Information</string>
    <string name="print_title_item_total_with_unit">$Total</string>
    <string name="order_num">Orders Num</string>
    <string name="payment_channels">Payment Method</string>
    <string name="payment_info">Payment Information</string>
    <string name="report_today">Today</string>
    <string name="report_yesterday">Yesterday</string>
    <string name="last_week">Last Week</string>
    <string name="last_month">Last Month</string>
    <string name="print_footer_thank_you">Powered by MPOS +855 1122 3328</string>
    <string name="print_footer_thank_you_eigthy">Powered by MPOS +855 1122 3328 www.m-pos.cc</string>
    <string name="print_report_discount_amount">Discount Amount</string>
    <string name="no_notice">暂无公告</string>
    <string name="sure">Sure</string>
    <string name="printer_title_goods_total_num">Total number of items</string>
    <string name="temporary_goods">Temporary Product</string>
    <string name="add_temporary_goods">Temporary Product</string>
    <string name="add_new_tmp_good">Add Temporary Product</string>
    <string name="product_name_required">Item Name <font color='#FF0000'>*</font></string>
    <string name="product_sale_price">Sales price($) <font color='#FF0000'>*</font></string>
    <string name="sure_to_delete_this_product">Sure to delete this item?</string>
    <string name="discount_whole_order">Whole Bill Discount</string>
    <string name="discount_single_good">Change unit price</string>
    <string name="edit_new_tmp_good">Edit Temporary Product</string>
    <string name="original_price">Original total price</string>
    <string name="please_enter_the_reason">Please enter a reason</string>
    <string name="new_price">New total price</string>
    <string name="exemption_type_require">Discount Types</string>
    <string name="percentage">Percentage</string>
    <string name="fixed_amount">Fixed Amount</string>
    <string name="temporary">Temporary</string>
    <string name="no_good_can_set_single_discount">There are no products that can be set On Item Discount</string>
    <string name="sale_price_discount">Sales Price deduct</string>
    <string name="vip_price_discount">Member Price deduct</string>
    <string name="contact_now">Contact Now</string>
    <string name="set_menu">Set Meal</string>
    <string name="clear_cart">Clear</string>
    <string name="change_table">Change Table</string>
    <string name="more">More</string>
    <string name="retreat">Return</string>
    <string name="modify_price">Change Price</string>
    <string name="discount2">Deduct</string>
    <string name="discount3">Discount</string>
    <string name="modify_sale_price">Change Sales Price</string>
    <string name="modify_vip_price">Change Member Price</string>
    <string name="original_single_price">Original Unit Price:</string>
    <string name="cancel_add_order">Cancel</string>
    <string name="classes_start">Start shift and openning cash</string>
    <string name="classes_close">Handover and logout</string>
    <string name="imprest">Handover and logout</string>
    <string name="save_imprest">Save openning cash</string>
    <string name="start_classes">Start shift</string>
    <string name="imprest_usd">Openning cash(USD)</string>
    <string name="imprest_khr">Openning cash(KHR)</string>
    <string name="expense_amount">Expense cash</string>
    <string name="transfer_amount">Handover cash <font color="#FF0000">*</font></string>
    <string name="closing_report">Closing Report</string>
    <string name="staff">Staff</string>
    <string name="class_start_time">Shift opening time</string>
    <string name="class_close_time">Shift handover time</string>
    <string name="online_payment_amount">Online payment</string>
    <string name="offline_payment_amount">Offline payment</string>
    <string name="cash_receipts">Cash payment</string>
    <string name="balance_receipts">Balance payment</string>
    <string name="reserve_fund">Openning cash</string>
    <string name="shift_expenses">On-duty expenses</string>
    <string name="shift_balance">End shift amount</string>
    <string name="shift_handover_records">Shift Record</string>
    <string name="imprest_">Openning cash：</string>
    <string name="shift_amount_">End shift amount：</string>
    <string name="handover_amount_">Handover cash：</string>
    <string name="order_amount_">Order Amount：</string>
    <string name="order_num_">Number Of Orders：</string>
    <string name="handover_cash">Handover cash</string>
    <string name="print_handover_report_tips">Sure want to print the [Closing Report]?</string>
    <string name="handover_tips">The previous staff has handed over the shift. Do you want to [Start shift] again?</string>
    <string name="deductible_amount">Reducible Amount</string>
    <string name="not_participating_in_discounted_products">Not participating in discount products</string>
    <string name="add_good_time">Order placed %d</string>
    <string name="meal_set_selected_content_str">(%s select %s, %s selected)</string>
    <string name="please_select_tag_to_delete">Please select the specification you want to delete</string>
    <string name="mealset_good_specifications">Specification %s: %s</string>
    <string name="mealset_delete_tag">Delete specification</string>
    <string name="amount_after_discount">Amount after discount</string>
    <string name="table_pre_order_status">To be paid</string>
    <string name="dining_time_in_hour">%1$d hour</string>
    <string name="dining_time_in_day">%1$d day</string>
    <string name="closing_report_ticket">Closing Report</string>
    <string name="store_configuration">Store Configuration</string>
    <string name="number_of_people_dining_is_required">Number of pax required</string>
    <string name="latest_purchase">Latest order</string>
    <string name="print_the_entire_order">Print whole bill</string>
    <string name="tax_ticket">Invoice</string>
    <string name="print_again_new_good">Reprint-latest order</string>
    <string name="print_again_all_good">Reprint-whole bill</string>
    <string name="auto_print_checkout_ticket">Automatically print checkout receipts</string>
    <string name="auto_print_checkout_ticket_cue1">The store doesn\'t uses [Invoice Number] and [Tax Information] at the same time, a Normal Receipt will be printed. Normal Receipt details can be customized.</string>
    <string name="auto_print_checkout_ticket_cue2">The store uses [Invoice Number] and [Tax Information]​ at the same time, an Invoice will be printed. Invoice details can be customized.</string>
    <string name="normal_ticket">Normal Receipt</string>
    <string name="usb_printer_not_connected">USB printer is not connected</string>
    <string name="printer_front_cover_open">Printer front cover open</string>
    <string name="printer_out_of_paper">Printer out of paper</string>
    <string name="feed_button_pressed">Feed button pressed</string>
    <string name="printer_error">Printer error, please try to restart the printer.</string>
    <string name="printer_unknow_error_restart">Printer unknown error, please try to restart the printer</string>
    <string name="config_conversion_ratio_require">Conversion Rate <font color='#FF0000'>*</font></string>
    <string name="change_conversion_ratio_history">Conversion ratio adjustment record</string>
    <string name="customize">Customize</string>
    <string name="sales_price_reduction_amount">Sales price reduction amount</string>
    <string name="vip_price_reduction_amount">Member price reduction amount</string>
    <string name="choose_discount_require">Select a reduction <font color='#FF0000'>*</font></string>
    <string name="please_enter_the_reason_require">Please enter the reason <font color='#FF0000'>*</font></string>
    <string name="no_discounts_available_at_the_moment">No discounts available</string>
    <string name="discounts2">Discount</string>
    <string name="limit_max">Capped</string>
    <string name="please_select_require">Please select <font color='#FF0000'>*</font></string>
    <string name="export">Export</string>
    <string name="classification_max_select">%s can purchase up to %s qty</string>
    <string name="classification_min_select">%s needs to purchase at least %s qty</string>
    <string name="exchange_rate_input_error_cue">The conversion rate is an integer between 4000-4200</string>
    <string name="export_formats_require">Choose File To Export <font color='#FF0000'>*</font></string>
    <string name="excel">EXCEL</string>
    <string name="pdf">PDF</string>
    <string name="save_at_location">Saved to %s</string>
    <string name="meal_set_selected_content_str2">(%s optional, %s selected)</string>
    <string name="please_select_take_out_platform">Please select a delivery platform </string>
    <string name="take_out_platform_require">Delivery platform <font color='#FF0000'>*</font></string>
    <string name="take_out_order_id_require">Delivery order number <font color='#FF0000'>*</font></string>
    <string name="please_input">Please enter...</string>
    <string name="take_out">Delivery</string>
    <string name="submit">Submit</string>
    <string name="commission">Commission</string>
    <string name="change_take_out_platform">Switch platform</string>
    <string name="take_out_platform">Delivery platform</string>
    <string name="take_out_order_id">Delivery order number</string>
    <string name="commission_percent">Commission ratio</string>
    <string name="modify_take_out_order_id">Edit delivery order number</string>
    <string name="reason_type_require">Reason type <font color='#FF0000'>*</font></string>
    <string name="pay_by_other">Other</string>
    <string name="take_out_platform_no_exist">The delivery platform doesn\'t exist</string>
    <string name="network_dns_resolution_failed">Network DNS resolution failed and the website cannot be accessed. Please change the network connection and try again</string>
    <string name="total2">Total</string>
    <string name="choose_discount_require2">Select Discount <font color='#FF0000'>*</font></string>
    <string name="passcode_has_been_set_successfully">Passcode has been set successfully.</string>
    <string name="sales_price_reduction">Sales Price deduct</string>
    <string name="vip_price_reduction">Member Price discount</string>
    <string name="mixed_payment">Combined Payment</string>
    <string name="customer">Customer</string>
    <string name="details">Details</string>
    <string name="customer_profile">头像</string>
    <string name="customer_nickname">Customer Name</string>
    <string name="customer_account">Customer Account</string>
    <string name="customer_balance">Customer Balance</string>
    <string name="channel_pay_amount">%s Payment Amount</string>
    <string name="modify_amount">Edit Amount</string>
    <string name="cash_pay_amount">Cash Payment Amount</string>
    <string name="balance_pay_amount">Balance Payment Amount</string>
    <string name="balance_pay_amount_not_zero">The balance payment amount cannot be 0</string>
    <string name="balance_pay_amount_must_small_than_total">The balance payment amount must be smaller than the total amount</string>
    <string name="payment_method_1">Payment Method 1</string>
    <string name="payment_method_2">Payment Method 2</string>
    <string name="pay_amount">Payment Amount</string>
    <string name="input_phone_number_required">Please enter phone number <font color='#FF0000'>*</font></string>
    <string name="difference_amount">Difference Amount</string>
    <string name="handover">End Shift</string>
    <string name="please_select">Please select</string>
    <string name="refund_pay_type">Refund Method</string>
    <string name="additional_gift_amount">Free Additional Top-up Amount</string>
    <string name="extra_coupon_giveaway">Free Additional Coupon</string>
    <string name="total_recharge_amount">Total Top-up Amount</string>
    <string name="payable_amount">Total</string>
    <string name="give_coupons">Free coupon</string>
    <string name="custom_recharge">Customize Top-up</string>
    <string name="unit_coupon">%s qty</string>
    <string name="coupon_details">Coupon Details</string>
    <string name="coupon_num">Quantity(qty)</string>
    <string name="export_ticket">Export Receipt</string>
    <string name="please_scan_to_order_food">Please scan to order food</string>
    <string name="expiry_time">Expiry Time</string>
    <string name="bring_this_receipt_with_you_when_checking_out">Please bring this receipt with you when checking out</string>
    <string name="sales_report">Sales Report</string>
    <string name="no_recharge_level">There is no top-up tier</string>
    <string name="tips_effect_time_after_get_day">Effective %s days after collecting, valid for %s days</string>
    <string name="credit">Credit</string>
    <string name="credit_amount">Credit Amount</string>
    <string name="receive_payment">Payment</string>
    <string name="edit_member">Edit Customer</string>
    <string name="cannot_be_less_than_s">Cannot be less than %s</string>
    <string name="search">Search</string>
    <string name="store_reports">Store Reports</string>
    <string name="actual_received">Amount Received</string>
    <string name="total_number_of_items">Total number of items</string>
    <string name="order_total_amount">Payment Amount($)</string>
    <string name="proportion_of_amount">Amount Ratio(%)</string>
    <string name="pay_channel">Payment Method</string>
    <string name="hint_sale_report_search">Product name, product serial no.</string>
    <string name="product_category">Product category</string>
    <string name="hint_sale_items_report_search">Order number, product name, product serial no.</string>
    <string name="order_total_num">Total Orders</string>
    <string name="select_date">Select Date</string>
    <string name="product_name">Item Name</string>
    <string name="pac">PAC</string>
    <string name="item_category">Item Category</string>
    <string name="credit_records">Credit Records</string>
    <string name="payment_records">Payment Records</string>
    <string name="view_more">View More</string>
    <string name="no_payment_records">No payment records</string>
    <string name="export_ticket_cue">Please set the product price in order to export</string>
    <string name="customer_nickname_required">Customer Name <font color='#FF0000'>*</font></string>
    <string name="cancel_credit">Cancelled Credit</string>
    <string name="order_has_no_price_good">The order contains items to be priced</string>
    <string name="store_profit_report_ticket">Store Profit Report</string>
    <string name="table_report_receipt">Table Report Receipt</string>
    <string name="set_time_price">Set daily-price</string>
    <string name="modify_time_price">Change daily-price</string>
    <string name="product_sale_price2">Sales Price <font color='#FF0000'>*</font></string>
    <string name="set_later">Set later</string>
    <string name="add_to_cart">Add to cart</string>
    <string name="time_price">Daily-price</string>
    <string name="time_price_parentheses">(Daily-price)</string>
    <string name="gift_product">赠品</string>
    <string name="weight_required">Weight <font color='#FF0000'>*</font></string>
    <string name="register_and_credit">Register and credit</string>
    <string name="credit_unpaid">Credit-Unpaid</string>
    <string name="credit_paid">Credit-Paid</string>
    <string name="credit_reason">Credit Reason</string>
    <string name="sure_to_cancel_credit">Sure to Cancel Credit?</string>
    <string name="repayment_time">Payment Time</string>
    <string name="repayment_amount">Payment Amount</string>
    <string name="validate_code">Verification code <font color='#FF0000'>*</font></string>
    <string name="get_validate_code">Send Code</string>
    <string name="edit_nickname">Change Name</string>
    <string name="edit_phone_number">Change Phone Number</string>
    <string name="retrieve">Resend</string>
    <string name="validate_code_sent">Verification code sent</string>
    <string name="no_coupon">No coupons available</string>
    <string name="total_good_num">共%s件</string>
    <string name="unregistered_member_tips">This mobile number is not registered as a member. Please fill in nickname and click Register</string>
    <string name="no_credit_records">No Credit Records</string>
    <string name="credit_success">Credit Successful</string>
    <string name="customer_account_required">Customer Account <font color='#FF0000'>*</font></string>
    <string name="cancel_top_up">Cancel Top-up</string>
    <string name="credit_payment">Credit Pay</string>
    <string name="credit_chart_title">Credit:</string>
    <string name="received_credit">Received Credit</string>
    <string name="enable_manual_input">Manual input enabled</string>
    <string name="weighing_goods">Weighing item</string>
    <string name="printer_retrying_message">Print task retry in progress (%1$d/%2$d)...</string>
    <string name="revenue_amount_tip">Total amount of [Confirmed/Unpaid/Paid/Pre-order/Partial Refund/Full Refund/Credit-unpaid/Credit-paid] orders by created time</string>
    <string name="unpaid_amount_tip">Total amount of [Unpaid/Confirmed] orders by created time</string>
    <string name="actual_revenue_tip">Total amount of [Paid/Pre-order/Partial Refund/Credit-unpaid/Credit-paid] orders by payment time, minus refunds</string>
    <string name="order_numbers_tip">Total orders by created time</string>
    <string name="unpaid_orders_tip">Total [Unpaid/Confirmed] orders by created time</string>
    <string name="paid_orders_tip">Total [Paid/Pre-order/Partial Refund/Credit-unpaid/Credit-paid] orders by payment time</string>
    <string name="refund_orders_tip">Total amount of [Refund] by refund time</string>
    <string name="refund_amount_tip">Actual refund amount by refund time</string>
    <string name="payment_method_tip">Amount of [Paid/Pre-order/Partial Refund/Credit-unpaid/Credit-paid] orders by payment method and payment time</string>
    <string name="member_number">Member Number</string>
    <string name="hint_order_id_and_phone_number">订单号/手机号码</string>
    <string name="log_out2">退出</string>
    <string name="go_to_handover">去交班</string>
    <string name="not_handover_logout_tips">当前未交班，确定要退出登录吗？</string>
    <string name="select_member_account">选择会员账户</string>
    <string name="search_member_hint">搜索会员昵称或手机号</string>
    <string name="no_member_found">未找到相关会员</string>
    <string name="no_members">暂无会员</string>
    <string name="member_avatar">会员头像</string>
    <string name="member">会员</string>
    <string name="balance_format">余额: %s</string>
    <string name="member_stats_format">消费%d次 · 充值%d次</string>
    <string name="search_member_empty_hint">输入会员号或者手机号查找</string>
    <string name="no_data_available">暂无数据</string>
</resources>