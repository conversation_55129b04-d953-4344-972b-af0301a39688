<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvOrderId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="987993196556010"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvOrderAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:textStyle="bold"
        tools:text="$11.50" />
</LinearLayout>
