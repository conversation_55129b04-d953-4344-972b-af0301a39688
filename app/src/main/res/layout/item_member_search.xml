<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:padding="16dp">

    <!-- 会员头像占位符 -->
    <ImageView
        android:id="@+id/ivMemberAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/background_circle_gray"
        android:contentDescription="@string/member_avatar"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_person"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 会员类型标签 -->
    <TextView
        android:id="@+id/tvMemberType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/background_member_normal"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/member"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 会员昵称 -->
    <TextView
        android:id="@+id/tvMemberName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/tvMemberType"
        app:layout_constraintStart_toEndOf="@id/ivMemberAvatar"
        app:layout_constraintTop_toTopOf="@id/ivMemberAvatar"
        tools:text="张三" />

    <!-- 手机号 -->
    <TextView
        android:id="@+id/tvMemberPhone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/tvMemberName"
        app:layout_constraintStart_toStartOf="@id/tvMemberName"
        app:layout_constraintTop_toBottomOf="@id/tvMemberName"
        tools:text="138****8888" />

    <!-- 余额信息 -->
    <TextView
        android:id="@+id/tvMemberBalance"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/primary"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/tvMemberName"
        app:layout_constraintStart_toStartOf="@id/tvMemberName"
        app:layout_constraintTop_toBottomOf="@id/tvMemberPhone"
        tools:text="余额: ¥1,234.56" />

    <!-- 统计信息 -->
    <TextView
        android:id="@+id/tvMemberStats"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/text_hint"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/tvMemberName"
        app:layout_constraintStart_toStartOf="@id/tvMemberName"
        app:layout_constraintTop_toBottomOf="@id/tvMemberBalance"
        tools:text="消费5次 · 充值3次" />

    <!-- 分割线 -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvMemberName"
        app:layout_constraintTop_toBottomOf="@id/tvMemberStats" />

</androidx.constraintlayout.widget.ConstraintLayout>
