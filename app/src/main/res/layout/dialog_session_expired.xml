<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/session_expired"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvContent"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:layout_weight="1"
            android:gravity="center"
            android:lineSpacingExtra="5dp"
            android:minLines="3"
            android:text="@string/login_information_expired_please_login_again"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnYes"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="30dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/back_to_login"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
