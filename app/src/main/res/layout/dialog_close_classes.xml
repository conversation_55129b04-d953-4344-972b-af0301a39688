<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:background="@drawable/background_white_left_radius_20"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/classes_close"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/btnClose"
                app:layout_constraintEnd_toStartOf="@+id/btnClose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btnClose" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clImprestInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:background="@drawable/background_dialog_info"
                android:padding="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle">

                <TextView
                    android:id="@+id/tvImprestUSD"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/imprest_usd"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvImprestUSDValue"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvImprestUSD"
                    app:layout_constraintTop_toTopOf="@+id/tvImprestUSD"
                    tools:text="10" />

                <TextView
                    android:id="@+id/tvImprestKHR"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/imprest_khr"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvImprestUSD" />

                <TextView
                    android:id="@+id/tvImprestKHRValue"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvImprestKHR"
                    app:layout_constraintTop_toTopOf="@+id/tvImprestKHR"
                    tools:text="10" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvExpenseAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:maxLines="2"
                android:text="@string/expense_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clImprestInfo" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilExpenseUSD"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="9dp"
                android:textColorHint="@color/bg_progress"
                app:layout_constraintEnd_toStartOf="@+id/tilExpenseKhmer"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvExpenseAmount">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtExpenseUSD"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableStart="@drawable/ic_dollar"
                    android:drawablePadding="10dp"
                    android:drawableTint="@color/black"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilExpenseKhmer"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColorHint="@color/bg_progress"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tilExpenseUSD"
                app:layout_constraintTop_toTopOf="@+id/tilExpenseUSD">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtExpenseKhmer"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:digits="0123456789"
                    android:drawableStart="@drawable/icon_km_unit"
                    android:drawablePadding="10dp"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/tvTransferAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:maxLines="2"
                android:text="@string/transfer_amount"
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tilExpenseUSD" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilTransferUSD"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="9dp"
                android:textColorHint="@color/bg_progress"
                app:layout_constraintEnd_toStartOf="@+id/tilTransferKhmer"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTransferAmount">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtTransferUSD"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableStart="@drawable/ic_dollar"
                    android:drawablePadding="10dp"
                    android:drawableTint="@color/black"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilTransferKhmer"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColorHint="@color/bg_progress"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tilTransferUSD"
                app:layout_constraintTop_toTopOf="@+id/tilTransferUSD">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtTransferKhmer"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:digits="0123456789"
                    android:drawableStart="@drawable/icon_km_unit"
                    android:drawablePadding="10dp"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilRemark"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/remark"
                android:textColorHint="@color/black60"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tilTransferUSD">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtRemark"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:inputType="text|textMultiLine"
                    android:maxLength="150"
                    android:maxLines="4"
                    android:minLines="4"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black"
                    android:textSize="@dimen/_14ssp" />

            </com.google.android.material.textfield.TextInputLayout>


            <!--            <TextView-->
            <!--                android:id="@+id/btnLogout"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="50dp"-->
            <!--                android:layout_marginTop="40dp"-->
            <!--                android:layout_marginBottom="24dp"-->
            <!--                android:background="@drawable/button_login_background"-->
            <!--                android:clickable="true"-->
            <!--                android:focusable="true"-->
            <!--                android:gravity="center"-->
            <!--                android:text="@string/classes_close"-->
            <!--                android:textColor="@color/mainWhite"-->
            <!--                android:textSize="16sp"-->
            <!--                app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                app:layout_constraintTop_toBottomOf="@+id/tilRemark" />-->

            <ProgressBar
                android:id="@+id/pbProgress"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView
            android:id="@+id/viewKeyBoard"
            android:layout_width="400dp"
            android:layout_height="match_parent"
            android:background="@drawable/background_e7e7e7_right_radius_20dp"/>
    </LinearLayout>


</FrameLayout>
