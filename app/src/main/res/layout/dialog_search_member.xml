<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/select_member_account"
            android:paddingBottom="16dp"/>

        <com.metathought.food_order.casheir.ui.widget.CustomSearchView
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:search_hint="@string/search_member_hint" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_weight="1">

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.scwang.smart.refresh.header.MaterialHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewMembers"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false" />

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

            <TextView
                android:id="@+id/textViewEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableTop="@drawable/ic_empty_members"
                android:drawablePadding="16dp"
                android:gravity="center"
                android:text="@string/no_members"
                android:textColor="@color/text_hint"
                android:textSize="16sp"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
